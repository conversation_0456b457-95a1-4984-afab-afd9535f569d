"""
Simple test script for the experimental film pipeline.
"""

import os
import sys
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_basic_functionality():
    """Test basic functionality without LLM dependencies."""
    
    logger.info("Testing basic video processing...")
    
    # Test video scanning
    try:
        from video_ingestion.local_video_scanner import LocalVideoScanner, VideoMetadata
        
        scanner = LocalVideoScanner()
        video_dir = Path("C:/Quick Share")
        
        if video_dir.exists():
            logger.info(f"Scanning videos in {video_dir}")
            videos = scanner.scan_directory(video_dir)
            logger.info(f"Found {len(videos)} videos")
            
            for video in videos[:3]:  # Test first 3 videos
                logger.info(f"Video: {video.filename} - Duration: {video.duration:.1f}s - Resolution: {video.width}x{video.height}")
        else:
            logger.error(f"Video directory not found: {video_dir}")
            return False
            
    except Exception as e:
        logger.error(f"Error in video scanning: {e}")
        return False
    
    # Test frame extraction
    try:
        from video_analysis.frame_extractor import FrameExtractor
        
        if videos:
            extractor = FrameExtractor()
            test_video = videos[0]
            
            logger.info(f"Testing frame extraction on {test_video.filename}")
            frames = extractor.extract_frames(test_video.path, start_time=0, end_time=5)
            logger.info(f"Extracted {len(frames)} frames from first 5 seconds")
            
            if frames:
                # Test saving a frame
                timestamp, frame = frames[0]
                output_path = Path("./test_frame.jpg")
                success = extractor.save_frame(frame, output_path)
                logger.info(f"Saved test frame: {success}")
        
    except Exception as e:
        logger.error(f"Error in frame extraction: {e}")
        return False
    
    # Test effects
    try:
        from effects.color_effects import ColorEffects
        from effects.datamosh import DatamoshEffect
        
        if frames:
            color_fx = ColorEffects()
            datamosh_fx = DatamoshEffect()
            
            # Test color effects
            _, test_frame = frames[0]
            
            logger.info("Testing color effects...")
            color_bleed = color_fx.apply_color_bleed(test_frame)
            color_shift = color_fx.apply_color_shift(test_frame)
            false_color = color_fx.apply_false_color(test_frame)
            
            logger.info("Color effects applied successfully")
            
            # Test datamosh on multiple frames
            if len(frames) > 3:
                logger.info("Testing datamosh effect...")
                frame_list = [frame for _, frame in frames[:5]]
                datamoshed = datamosh_fx.apply_datamosh(frame_list)
                logger.info(f"Datamosh applied to {len(datamoshed)} frames")
        
    except Exception as e:
        logger.error(f"Error in effects testing: {e}")
        return False
    
    # Test new effects
    try:
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        
        if frames:
            warp_fx = FrameWarpingEffect()
            glitch_fx = GlitchEffects()
            
            _, test_frame = frames[0]
            
            logger.info("Testing frame warping effects...")
            wave_distorted = warp_fx.apply_wave_distortion(test_frame)
            spiral_distorted = warp_fx.apply_spiral_distortion(test_frame)
            fisheye = warp_fx.apply_fisheye_distortion(test_frame)
            kaleidoscope = warp_fx.apply_kaleidoscope(test_frame)
            
            logger.info("Testing glitch effects...")
            digital_noise = glitch_fx.apply_digital_noise(test_frame)
            scan_lines = glitch_fx.apply_scan_lines(test_frame)
            rgb_shift = glitch_fx.apply_rgb_shift(test_frame)
            
            logger.info("Advanced effects applied successfully")
    
    except Exception as e:
        logger.error(f"Error in advanced effects testing: {e}")
        return False
    
    logger.info("✅ Basic functionality test completed successfully!")
    return True

def test_video_composition():
    """Test video composition without LLM."""
    
    logger.info("Testing video composition...")
    
    try:
        from editing.video_compositor import VideoCompositor
        from pathlib import Path
        
        # Create compositor
        compositor = VideoCompositor()
        
        # Find a test video
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No MP4 files found for testing")
            return False
        
        test_video = video_files[0]
        logger.info(f"Using test video: {test_video.name}")
        
        # Add a short segment with effects
        compositor.add_segment(
            video_path=test_video,
            start_time=0,
            end_time=5,  # 5 second segment
            effects=['color_bleed', 'wave_distortion'],
            target_duration=5
        )
        
        # Add another segment with different effects
        compositor.add_segment(
            video_path=test_video,
            start_time=5,
            end_time=10,
            effects=['digital_noise', 'rgb_shift'],
            target_duration=5
        )
        
        # Render test video
        output_path = Path("./output/test_experimental.mp4")
        output_path.parent.mkdir(exist_ok=True)
        
        logger.info("Rendering experimental video...")
        success = compositor.render(output_path, fps=24, resolution=(1280, 720))
        
        if success:
            logger.info(f"✅ Experimental video created: {output_path}")
            logger.info(f"File size: {output_path.stat().st_size / 1024 / 1024:.1f} MB")
        else:
            logger.error("❌ Video rendering failed")
            return False
        
    except Exception as e:
        logger.error(f"Error in video composition: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def create_simple_experimental_film():
    """Create a simple experimental film using available videos."""
    
    logger.info("Creating simple experimental film...")
    
    try:
        from editing.video_compositor import VideoCompositor
        from pathlib import Path
        import random
        
        # Get all videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if len(video_files) < 2:
            logger.error("Need at least 2 videos for experimental film")
            return False
        
        # Create compositor
        compositor = VideoCompositor()
        
        # Define experimental segments
        experimental_effects = [
            ['color_bleed', 'wave_distortion'],
            ['datamosh', 'digital_noise'],
            ['rgb_shift', 'scan_lines'],
            ['spiral_distortion', 'vhs_artifacts'],
            ['kaleidoscope', 'block_corruption'],
            ['fisheye', 'color_shift']
        ]
        
        total_duration = 0
        target_film_duration = 30  # 30 second experimental film
        
        logger.info("Adding experimental segments...")
        
        for i, video_file in enumerate(video_files):
            if total_duration >= target_film_duration:
                break
            
            # Random segment duration between 3-8 seconds
            segment_duration = random.uniform(3, 8)
            
            # Random start time (first 30 seconds of source video)
            start_time = random.uniform(0, min(30, max(10, segment_duration + 5)))
            end_time = start_time + segment_duration
            
            # Random effects
            effects = random.choice(experimental_effects)
            
            logger.info(f"Segment {i+1}: {video_file.name} ({start_time:.1f}s-{end_time:.1f}s) with {effects}")
            
            compositor.add_segment(
                video_path=video_file,
                start_time=start_time,
                end_time=end_time,
                effects=effects,
                target_duration=segment_duration
            )
            
            total_duration += segment_duration
        
        # Render experimental film
        output_path = Path("./output/experimental_film.mp4")
        output_path.parent.mkdir(exist_ok=True)
        
        logger.info(f"Rendering {total_duration:.1f}s experimental film...")
        success = compositor.render(output_path, fps=24, resolution=(1280, 720))
        
        if success:
            logger.info(f"🎬 Experimental film created: {output_path}")
            logger.info(f"Duration: {total_duration:.1f}s")
            logger.info(f"File size: {output_path.stat().st_size / 1024 / 1024:.1f} MB")
            return True
        else:
            logger.error("❌ Experimental film rendering failed")
            return False
        
    except Exception as e:
        logger.error(f"Error creating experimental film: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("🎥 Starting Experimental Film Pipeline Test")
    
    # Test 1: Basic functionality
    if not test_basic_functionality():
        logger.error("❌ Basic functionality test failed")
        sys.exit(1)
    
    # Test 2: Video composition
    if not test_video_composition():
        logger.error("❌ Video composition test failed")
        sys.exit(1)
    
    # Test 3: Create experimental film
    if not create_simple_experimental_film():
        logger.error("❌ Experimental film creation failed")
        sys.exit(1)
    
    logger.info("🎉 All tests completed successfully!")
    logger.info("Check the ./output/ directory for generated videos")
