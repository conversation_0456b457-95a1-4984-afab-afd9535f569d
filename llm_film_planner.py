"""
LLM-driven film planning system using Gemini function calling.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging
from typing import List, Dict, Any
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global effects instances
color_fx = None
warp_fx = None
glitch_fx = None
datamosh_fx = None
recursive_fx = None

def initialize_effects():
    """Initialize all effects instances."""
    global color_fx, warp_fx, glitch_fx, datamosh_fx, recursive_fx
    
    from effects.color_effects import ColorEffects
    from effects.frame_warping import FrameWarpingEffect
    from effects.glitch_effects import GlitchEffects
    from effects.datamosh import DatamoshEffect
    from effects.recursive_effects import RecursiveEffects
    
    color_fx = ColorEffects()
    warp_fx = FrameWarpingEffect()
    glitch_fx = GlitchEffects()
    datamosh_fx = DatamoshEffect()
    recursive_fx = RecursiveEffects()
    
    logger.info("Effects initialized successfully")

# Effect functions that the LLM can call
def apply_color_bleed(frames: List[np.ndarray], intensity: float = 0.5) -> List[np.ndarray]:
    """Apply color bleeding effect to frames."""
    return [color_fx.apply_color_bleed(frame, intensity) for frame in frames]

def apply_color_shift(frames: List[np.ndarray]) -> List[np.ndarray]:
    """Apply color channel shifting to frames."""
    return [color_fx.apply_color_shift(frame) for frame in frames]

def apply_chromatic_aberration(frames: List[np.ndarray], intensity: float = 0.5) -> List[np.ndarray]:
    """Apply chromatic aberration effect to frames."""
    return [color_fx.apply_chromatic_aberration(frame, intensity) for frame in frames]

def apply_false_color(frames: List[np.ndarray], color_map: str = "thermal") -> List[np.ndarray]:
    """Apply false color mapping to frames."""
    return [color_fx.apply_false_color(frame, color_map) for frame in frames]

def apply_wave_distortion(frames: List[np.ndarray], amplitude: float = 20, frequency: float = 0.1) -> List[np.ndarray]:
    """Apply wave distortion to frames."""
    return [warp_fx.apply_wave_distortion(frame, amplitude, frequency) for frame in frames]

def apply_spiral_distortion(frames: List[np.ndarray], intensity: float = 0.5) -> List[np.ndarray]:
    """Apply spiral distortion to frames."""
    return [warp_fx.apply_spiral_distortion(frame, intensity) for frame in frames]

def apply_kaleidoscope(frames: List[np.ndarray], segments: int = 6) -> List[np.ndarray]:
    """Apply kaleidoscope effect to frames."""
    return [warp_fx.apply_kaleidoscope(frame, segments) for frame in frames]

def apply_fisheye(frames: List[np.ndarray], strength: float = 0.5) -> List[np.ndarray]:
    """Apply fisheye distortion to frames."""
    return [warp_fx.apply_fisheye_distortion(frame, strength) for frame in frames]

def apply_mirror_effect(frames: List[np.ndarray], direction: str = "horizontal") -> List[np.ndarray]:
    """Apply mirror effect to frames."""
    return [warp_fx.apply_mirror_effect(frame, direction) for frame in frames]

def apply_pixelation(frames: List[np.ndarray], pixel_size: int = 10) -> List[np.ndarray]:
    """Apply pixelation effect to frames."""
    return [warp_fx.apply_pixelation(frame, pixel_size) for frame in frames]

def apply_digital_noise(frames: List[np.ndarray], intensity: float = 0.3) -> List[np.ndarray]:
    """Apply digital noise to frames."""
    return [glitch_fx.apply_digital_noise(frame, intensity) for frame in frames]

def apply_scan_lines(frames: List[np.ndarray], intensity: float = 0.5) -> List[np.ndarray]:
    """Apply scan line effect to frames."""
    return [glitch_fx.apply_scan_lines(frame, intensity=intensity) for frame in frames]

def apply_rgb_shift(frames: List[np.ndarray], shift_amount: int = 5) -> List[np.ndarray]:
    """Apply RGB channel shift to frames."""
    return [glitch_fx.apply_rgb_shift(frame, shift_amount) for frame in frames]

def apply_vhs_artifacts(frames: List[np.ndarray]) -> List[np.ndarray]:
    """Apply VHS artifacts to frames."""
    return [glitch_fx.apply_vhs_artifacts(frame) for frame in frames]

def apply_datamosh(frames: List[np.ndarray], intensity: float = 0.5) -> List[np.ndarray]:
    """Apply datamoshing effect to frame sequence."""
    return datamosh_fx.apply_datamosh(frames, intensity)

def apply_video_feedback(frames: List[np.ndarray], feedback_strength: float = 0.3) -> List[np.ndarray]:
    """Apply video feedback effect to frames."""
    return recursive_fx.apply_video_feedback(frames, feedback_strength)

def apply_temporal_echo(frames: List[np.ndarray], echo_frames: int = 5) -> List[np.ndarray]:
    """Apply temporal echo effect to frames."""
    return recursive_fx.apply_temporal_echo(frames, echo_frames)

# Function declarations for Gemini
EFFECT_FUNCTIONS = [
    {
        "name": "apply_color_bleed",
        "description": "Apply color bleeding effect where color channels separate and bleed into each other",
        "parameters": {
            "type": "object",
            "properties": {
                "intensity": {
                    "type": "number",
                    "description": "Intensity of the color bleeding effect (0.0 to 1.0)",
                    "minimum": 0.0,
                    "maximum": 1.0
                }
            }
        }
    },
    {
        "name": "apply_color_shift",
        "description": "Apply random color channel shifting for psychedelic effects",
        "parameters": {
            "type": "object",
            "properties": {}
        }
    },
    {
        "name": "apply_chromatic_aberration",
        "description": "Apply chromatic aberration effect like old camera lenses",
        "parameters": {
            "type": "object",
            "properties": {
                "intensity": {
                    "type": "number",
                    "description": "Intensity of chromatic aberration (0.0 to 1.0)",
                    "minimum": 0.0,
                    "maximum": 1.0
                }
            }
        }
    },
    {
        "name": "apply_false_color",
        "description": "Apply false color mapping for thermal/scientific visualization effects",
        "parameters": {
            "type": "object",
            "properties": {
                "color_map": {
                    "type": "string",
                    "description": "Color map to use",
                    "enum": ["thermal", "rainbow", "random"]
                }
            }
        }
    },
    {
        "name": "apply_wave_distortion",
        "description": "Apply wave-like distortion to create flowing, liquid effects",
        "parameters": {
            "type": "object",
            "properties": {
                "amplitude": {
                    "type": "number",
                    "description": "Wave amplitude (10-50)",
                    "minimum": 10,
                    "maximum": 50
                },
                "frequency": {
                    "type": "number",
                    "description": "Wave frequency (0.01-0.2)",
                    "minimum": 0.01,
                    "maximum": 0.2
                }
            }
        }
    },
    {
        "name": "apply_spiral_distortion",
        "description": "Apply spiral distortion for hypnotic, swirling effects",
        "parameters": {
            "type": "object",
            "properties": {
                "intensity": {
                    "type": "number",
                    "description": "Spiral intensity (0.0 to 1.0)",
                    "minimum": 0.0,
                    "maximum": 1.0
                }
            }
        }
    },
    {
        "name": "apply_kaleidoscope",
        "description": "Apply kaleidoscope effect for symmetrical, mandala-like patterns",
        "parameters": {
            "type": "object",
            "properties": {
                "segments": {
                    "type": "integer",
                    "description": "Number of kaleidoscope segments (3-12)",
                    "minimum": 3,
                    "maximum": 12
                }
            }
        }
    },
    {
        "name": "apply_digital_noise",
        "description": "Apply digital noise for glitch and corruption effects",
        "parameters": {
            "type": "object",
            "properties": {
                "intensity": {
                    "type": "number",
                    "description": "Noise intensity (0.0 to 1.0)",
                    "minimum": 0.0,
                    "maximum": 1.0
                }
            }
        }
    },
    {
        "name": "apply_datamosh",
        "description": "Apply datamoshing effect for motion vector corruption and digital artifacts",
        "parameters": {
            "type": "object",
            "properties": {
                "intensity": {
                    "type": "number",
                    "description": "Datamosh intensity (0.0 to 1.0)",
                    "minimum": 0.0,
                    "maximum": 1.0
                }
            }
        }
    },
    {
        "name": "apply_video_feedback",
        "description": "Apply video feedback loops for recursive, echoing effects",
        "parameters": {
            "type": "object",
            "properties": {
                "feedback_strength": {
                    "type": "number",
                    "description": "Feedback strength (0.0 to 1.0)",
                    "minimum": 0.0,
                    "maximum": 1.0
                }
            }
        }
    }
]

class LLMFilmPlanner:
    """LLM-driven film planner using Gemini function calling."""
    
    def __init__(self):
        self.client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        self.tools = types.Tool(function_declarations=EFFECT_FUNCTIONS)
        self.config = types.GenerateContentConfig(tools=[self.tools])
        
        # Initialize effects
        initialize_effects()
        
        # Function mapping
        self.function_map = {
            "apply_color_bleed": apply_color_bleed,
            "apply_color_shift": apply_color_shift,
            "apply_chromatic_aberration": apply_chromatic_aberration,
            "apply_false_color": apply_false_color,
            "apply_wave_distortion": apply_wave_distortion,
            "apply_spiral_distortion": apply_spiral_distortion,
            "apply_kaleidoscope": apply_kaleidoscope,
            "apply_digital_noise": apply_digital_noise,
            "apply_datamosh": apply_datamosh,
            "apply_video_feedback": apply_video_feedback,
        }
    
    def create_experimental_film_plan(self, concept: str, style: str = "experimental") -> Dict[str, Any]:
        """Create a film plan using LLM function calling."""
        
        prompt = f"""Create an experimental film plan for the concept: "{concept}"
        
        Style: {style}
        
        You are an experimental filmmaker with access to advanced video effects. Create a detailed plan that uses the available effects functions to create a compelling experimental film.
        
        Consider:
        - The emotional journey of the concept
        - How different effects can represent different aspects
        - Building intensity and creating visual flow
        - Combining effects for unique results
        
        Plan a sequence of 4-6 segments, each using different effects to explore the concept. For each segment, call the appropriate effect functions with suitable parameters.
        
        Available effects include color manipulation, distortion, glitch effects, and recursive feedback loops."""
        
        try:
            response = self.client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=prompt,
                config=self.config
            )
            
            plan = {
                "concept": concept,
                "style": style,
                "segments": [],
                "llm_response": response.text if hasattr(response, 'text') else ""
            }
            
            # Process function calls
            if (response.candidates and 
                response.candidates[0].content.parts):
                
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'function_call') and part.function_call:
                        function_call = part.function_call
                        function_name = function_call.name
                        function_args = dict(function_call.args) if function_call.args else {}
                        
                        segment = {
                            "effect_function": function_name,
                            "parameters": function_args,
                            "description": f"Apply {function_name.replace('_', ' ')} effect"
                        }
                        plan["segments"].append(segment)
                        
                        logger.info(f"LLM planned segment: {function_name} with args {function_args}")
            
            return plan
            
        except Exception as e:
            logger.error(f"Error creating film plan: {e}")
            return {
                "concept": concept,
                "style": style,
                "segments": [],
                "error": str(e)
            }
    
    def execute_film_plan(self, plan: Dict[str, Any], source_frames: List[np.ndarray]) -> List[np.ndarray]:
        """Execute the film plan by applying the planned effects."""
        
        if not plan.get("segments"):
            logger.warning("No segments in plan, returning original frames")
            return source_frames
        
        processed_frames = source_frames.copy()
        
        logger.info(f"Executing film plan with {len(plan['segments'])} segments")
        
        for i, segment in enumerate(plan["segments"]):
            function_name = segment["effect_function"]
            parameters = segment.get("parameters", {})
            
            if function_name in self.function_map:
                try:
                    logger.info(f"Applying {function_name} with parameters {parameters}")
                    effect_function = self.function_map[function_name]
                    processed_frames = effect_function(processed_frames, **parameters)
                    logger.info(f"Successfully applied {function_name}")
                    
                except Exception as e:
                    logger.error(f"Error applying {function_name}: {e}")
                    # Continue with next effect
            else:
                logger.warning(f"Unknown function: {function_name}")
        
        return processed_frames
