"""
Configuration management for the LLM-driven Experimental Film Pipeline.
"""

import os
from pathlib import Path
from typing import Optional, List
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class LLMConfig(BaseSettings):
    """LLM API configuration."""
    
    # OpenAI Configuration
    openai_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))
    openai_model: str = "gpt-4-vision-preview"
    
    # Gemini Configuration
    gemini_api_key: Optional[str] = Field(default_factory=lambda: os.getenv("GEMINI_API_KEY"))
    gemini_model: str = "gemini-2.5-flash-preview-04-17"
    
    # Default LLM provider
    default_provider: str = "openai"  # or "gemini"
    
    # API limits
    max_tokens: int = 4000
    temperature: float = 0.7
    max_retries: int = 3


class VideoConfig(BaseSettings):
    """Video processing configuration."""
    
    # Input/Output directories
    input_dir: Path = Path("./input_videos")
    output_dir: Path = Path("./output")
    cache_dir: Path = Path("./cache")
    
    # Video processing settings
    max_video_duration: int = 3600  # seconds (1 hour)
    frame_extraction_fps: float = 1.0  # Extract 1 frame per second
    max_frame_size: tuple = (1920, 1080)
    
    # YouTube download settings
    youtube_quality: str = "720p"
    youtube_format: str = "mp4"
    
    # Supported video formats
    supported_formats: List[str] = [".mp4", ".avi", ".mov", ".mkv", ".webm"]


class EffectsConfig(BaseSettings):
    """Experimental effects configuration."""
    
    # Datamosh settings
    datamosh_intensity: float = 0.5
    datamosh_frequency: int = 10  # frames between datamosh effects
    
    # Color effects
    color_bleed_intensity: float = 0.3
    color_shift_range: tuple = (-50, 50)
    
    # Frame warping
    warp_intensity: float = 0.2
    recursive_depth: int = 3
    
    # Glitch effects
    glitch_probability: float = 0.1
    noise_intensity: float = 0.15


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    
    database_url: str = "sqlite:///./film_pipeline.db"
    echo_sql: bool = False
    
    # Index settings
    embedding_dimension: int = 1536  # OpenAI embedding dimension
    similarity_threshold: float = 0.7


class AppConfig(BaseSettings):
    """Main application configuration."""
    
    # Component configurations
    llm: LLMConfig = LLMConfig()
    video: VideoConfig = VideoConfig()
    effects: EffectsConfig = EffectsConfig()
    database: DatabaseConfig = DatabaseConfig()
    
    # Application settings
    debug: bool = False
    log_level: str = "INFO"
    
    # Web interface
    web_host: str = "localhost"
    web_port: int = 8000
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure required directories exist."""
        for dir_path in [self.video.input_dir, self.video.output_dir, self.video.cache_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)


# Global configuration instance
config = AppConfig()
