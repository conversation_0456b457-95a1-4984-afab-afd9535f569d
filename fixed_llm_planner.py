"""
Fixed LLM film planner that creates proper full-length videos.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging
from dotenv import load_dotenv
from google import genai
import random

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedLLMPlanner:
    """Fixed LLM planner that creates proper full-length videos."""
    
    def __init__(self):
        self.client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Initialize effects
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        
        self.color_fx = ColorEffects()
        self.warp_fx = FrameWarpingEffect()
        self.glitch_fx = GlitchEffects()
        self.datamosh_fx = DatamoshEffect()
    
    def create_film_plan(self, concept: str, style: str = "experimental") -> dict:
        """Create a film plan using LLM reasoning."""
        
        prompt = f"""Create an experimental film plan for the concept: "{concept}" with style: "{style}"

You are an experimental filmmaker. Create a detailed plan with 4-6 segments that explore this concept through visual effects.

For each segment, specify:
1. A brief description of what it represents
2. The primary effect to use
3. The intensity/parameters for the effect
4. The duration in seconds (total should be 20-30 seconds)

Available effects:
- color_bleed: Separates color channels for psychedelic effects (intensity 0.1-1.0)
- color_shift: Random color channel shifting (no parameters)
- chromatic_aberration: Lens-like color separation (intensity 0.1-1.0)
- wave_distortion: Flowing liquid effects (amplitude 10-50, frequency 0.01-0.2)
- spiral_distortion: Hypnotic swirling (intensity 0.1-1.0)
- kaleidoscope: Symmetrical patterns (segments 3-12)
- digital_noise: Glitch corruption (intensity 0.1-1.0)
- rgb_shift: Channel displacement (shift_amount 1-20)
- scan_lines: TV-like artifacts (intensity 0.1-1.0)
- datamosh: Motion corruption (intensity 0.1-1.0)

Respond with a structured plan in this format:
SEGMENT 1: [description]
Effect: [effect_name]
Parameters: [parameter=value, ...]
Duration: [seconds]
Mood: [mood description]

SEGMENT 2: [description]
...

Make sure the total duration is 20-30 seconds and each segment has a clear duration."""
        
        try:
            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=prompt
            )
            
            plan_text = response.text
            logger.info(f"LLM Plan Response:\n{plan_text}")
            
            # Parse the plan
            segments = self._parse_plan(plan_text)
            
            return {
                "concept": concept,
                "style": style,
                "plan_text": plan_text,
                "segments": segments
            }
            
        except Exception as e:
            logger.error(f"Error creating plan: {e}")
            return {"concept": concept, "style": style, "segments": [], "error": str(e)}
    
    def _parse_plan(self, plan_text: str) -> list:
        """Parse the LLM plan text into structured segments."""
        
        segments = []
        lines = plan_text.split('\n')
        current_segment = None
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('SEGMENT'):
                if current_segment:
                    segments.append(current_segment)
                
                # Extract description
                description = line.split(':', 1)[1].strip() if ':' in line else "Experimental segment"
                current_segment = {
                    "description": description,
                    "effect": None,
                    "parameters": {},
                    "duration": 5.0,  # Default duration
                    "mood": ""
                }
            
            elif line.startswith('Effect:') and current_segment:
                effect = line.split(':', 1)[1].strip()
                current_segment["effect"] = effect
            
            elif line.startswith('Parameters:') and current_segment:
                params_text = line.split(':', 1)[1].strip()
                current_segment["parameters"] = self._parse_parameters(params_text)
            
            elif line.startswith('Duration:') and current_segment:
                duration_text = line.split(':', 1)[1].strip()
                try:
                    # Extract number from duration text
                    import re
                    match = re.search(r'([0-9.]+)', duration_text)
                    if match:
                        current_segment["duration"] = float(match.group(1))
                except:
                    current_segment["duration"] = 5.0
            
            elif line.startswith('Mood:') and current_segment:
                mood = line.split(':', 1)[1].strip()
                current_segment["mood"] = mood
        
        # Add final segment
        if current_segment:
            segments.append(current_segment)
        
        return segments
    
    def _parse_parameters(self, params_text: str) -> dict:
        """Parse parameter text into dictionary."""
        
        params = {}
        
        # Simple parsing for common parameters
        if "intensity" in params_text.lower():
            try:
                import re
                match = re.search(r'intensity[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["intensity"] = float(match.group(1))
            except:
                params["intensity"] = 0.5
        
        if "amplitude" in params_text.lower():
            try:
                match = re.search(r'amplitude[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["amplitude"] = float(match.group(1))
            except:
                params["amplitude"] = 20
        
        if "segments" in params_text.lower():
            try:
                match = re.search(r'segments[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["segments"] = int(match.group(1))
            except:
                params["segments"] = 6
        
        if "shift_amount" in params_text.lower():
            try:
                match = re.search(r'shift_amount[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["shift_amount"] = int(match.group(1))
            except:
                params["shift_amount"] = 5
        
        return params
    
    def execute_plan_properly(self, plan: dict, video_files: list, target_duration: float = 25.0) -> list:
        """Execute the film plan properly with full-length video."""
        
        if not plan.get("segments"):
            logger.warning("No segments to execute")
            return []
        
        from video_analysis.frame_extractor import FrameExtractor
        extractor = FrameExtractor()
        
        all_processed_frames = []
        fps = 24
        
        logger.info(f"Creating {target_duration}s film with {len(plan['segments'])} segments")
        
        # Calculate actual segment durations
        total_planned_duration = sum(seg.get("duration", 5.0) for seg in plan["segments"])
        if total_planned_duration > 0:
            duration_scale = target_duration / total_planned_duration
        else:
            duration_scale = 1.0
        
        for i, segment in enumerate(plan["segments"]):
            effect = segment.get("effect", "").lower()
            params = segment.get("parameters", {})
            segment_duration = segment.get("duration", 5.0) * duration_scale
            frames_needed = int(segment_duration * fps)
            
            logger.info(f"Segment {i+1}: {segment['description']}")
            logger.info(f"  Effect: {effect}")
            logger.info(f"  Duration: {segment_duration:.1f}s ({frames_needed} frames)")
            logger.info(f"  Parameters: {params}")
            
            # Get source video for this segment
            source_video = video_files[i % len(video_files)]
            
            # Extract enough frames for this segment
            start_time = random.uniform(0, 15)  # Random start point
            end_time = start_time + segment_duration + 2  # Extra buffer
            
            frames = extractor.extract_frames(source_video, start_time=start_time, end_time=end_time)
            
            if not frames:
                logger.warning(f"No frames extracted for segment {i+1}")
                # Create black frames as fallback
                black_frame = np.zeros((720, 1280, 3), dtype=np.uint8)
                segment_frames = [black_frame] * frames_needed
            else:
                # Convert to frame list
                frame_list = [frame for _, frame in frames]
                
                # Ensure we have enough frames by repeating if necessary
                while len(frame_list) < frames_needed:
                    frame_list.extend(frame_list[:min(len(frame_list), frames_needed - len(frame_list))])
                
                # Trim to exact number needed
                segment_frames = frame_list[:frames_needed]
                
                # Apply the effect to this segment
                try:
                    if "color_bleed" in effect:
                        intensity = params.get("intensity", 0.5)
                        segment_frames = [self.color_fx.apply_color_bleed(f, intensity) for f in segment_frames]
                    
                    elif "color_shift" in effect:
                        segment_frames = [self.color_fx.apply_color_shift(f) for f in segment_frames]
                    
                    elif "chromatic_aberration" in effect:
                        intensity = params.get("intensity", 0.5)
                        segment_frames = [self.color_fx.apply_chromatic_aberration(f, intensity) for f in segment_frames]
                    
                    elif "wave_distortion" in effect:
                        amplitude = params.get("amplitude", 20)
                        segment_frames = [self.warp_fx.apply_wave_distortion(f, amplitude) for f in segment_frames]
                    
                    elif "spiral_distortion" in effect:
                        intensity = params.get("intensity", 0.5)
                        segment_frames = [self.warp_fx.apply_spiral_distortion(f, intensity) for f in segment_frames]
                    
                    elif "kaleidoscope" in effect:
                        segments = params.get("segments", 6)
                        segment_frames = [self.warp_fx.apply_kaleidoscope(f, segments) for f in segment_frames]
                    
                    elif "digital_noise" in effect:
                        intensity = params.get("intensity", 0.3)
                        segment_frames = [self.glitch_fx.apply_digital_noise(f, intensity) for f in segment_frames]
                    
                    elif "rgb_shift" in effect:
                        shift_amount = params.get("shift_amount", 5)
                        segment_frames = [self.glitch_fx.apply_rgb_shift(f, shift_amount) for f in segment_frames]
                    
                    elif "scan_lines" in effect:
                        intensity = params.get("intensity", 0.5)
                        segment_frames = [self.glitch_fx.apply_scan_lines(f, intensity=intensity) for f in segment_frames]
                    
                    elif "datamosh" in effect:
                        intensity = params.get("intensity", 0.5)
                        segment_frames = self.datamosh_fx.apply_datamosh(segment_frames, intensity)
                    
                    else:
                        logger.warning(f"Unknown effect: {effect}")
                    
                    logger.info(f"  ✅ Applied {effect} to {len(segment_frames)} frames")
                    
                except Exception as e:
                    logger.error(f"  ❌ Error applying {effect}: {e}")
            
            # Add this segment's frames to the final video
            all_processed_frames.extend(segment_frames)
        
        logger.info(f"Total frames created: {len(all_processed_frames)} ({len(all_processed_frames)/fps:.1f}s)")
        return all_processed_frames

def test_fixed_planner():
    """Test the fixed LLM planner."""
    
    logger.info("🎬 Testing Fixed LLM Planner")
    
    try:
        # Initialize planner
        planner = FixedLLMPlanner()
        
        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found")
            return False
        
        logger.info(f"Found {len(video_files)} source videos")
        
        # Test concepts with longer durations
        concepts = [
            ("digital dreams", "psychedelic"),
            ("memory fragments", "nostalgic"),
            ("cyber anxiety", "aggressive")
        ]
        
        for concept, style in concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎬 Creating FULL-LENGTH plan for: '{concept}' ({style})")
            logger.info(f"{'='*60}")
            
            # Create plan
            plan = planner.create_film_plan(concept, style)
            
            if plan.get("error"):
                logger.error(f"Plan creation failed: {plan['error']}")
                continue
            
            # Show plan details
            total_duration = sum(seg.get("duration", 5.0) for seg in plan["segments"])
            logger.info(f"Plan has {len(plan['segments'])} segments, total duration: {total_duration:.1f}s")
            
            # Execute plan with proper duration
            processed_frames = planner.execute_plan_properly(plan, video_files, target_duration=25.0)
            
            if not processed_frames:
                logger.error("No frames generated")
                continue
            
            # Save result
            output_path = Path(f"./output/fixed_llm_{concept.replace(' ', '_')}.mp4")
            output_path.parent.mkdir(exist_ok=True)
            
            # Save as video
            if save_frames_as_video(processed_frames, output_path):
                file_size = output_path.stat().st_size / 1024 / 1024
                duration = len(processed_frames) / 24
                logger.info(f"✅ Created: {output_path}")
                logger.info(f"   Duration: {duration:.1f}s, Size: {file_size:.1f}MB")
            else:
                logger.error(f"❌ Failed to save: {output_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in fixed planner test: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_frames_as_video(frames: list, output_path: Path, fps: int = 24) -> bool:
    """Save frames as video."""
    
    if not frames:
        return False
    
    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
        
        if not out.isOpened():
            logger.error("Failed to create video writer")
            return False
        
        for i, frame in enumerate(frames):
            # Resize frame to consistent size
            frame_resized = cv2.resize(frame, (width, height))
            out.write(frame_resized)
            
            if i % 100 == 0:
                logger.info(f"Writing frame {i}/{len(frames)}")
        
        out.release()
        return output_path.exists()
        
    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False

if __name__ == "__main__":
    test_fixed_planner()
