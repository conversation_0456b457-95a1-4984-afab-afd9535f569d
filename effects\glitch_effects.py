"""
Advanced glitch effects for experimental video.
"""

import logging
import numpy as np
import cv2
from typing import List, Optional, Tuple
import random

try:
    from config import config
except ImportError:
    # Fallback config
    class Config:
        class effects:
            glitch_probability = 0.1
            noise_intensity = 0.15
    config = Config()

logger = logging.getLogger(__name__)


class GlitchEffects:
    """Implements various glitch and digital artifact effects."""

    def __init__(self):
        self.glitch_probability = config.effects.glitch_probability
        self.noise_intensity = config.effects.noise_intensity

    def apply_digital_noise(self, frame: np.ndarray, intensity: float = None) -> np.ndarray:
        """
        Apply digital noise to frame.

        Args:
            frame: Input frame
            intensity: Noise intensity (0.0 to 1.0)

        Returns:
            Frame with digital noise
        """
        intensity = intensity or self.noise_intensity

        # Generate noise
        noise = np.random.randint(0, int(255 * intensity), frame.shape, dtype=np.uint8)

        # Apply noise with random probability
        mask = np.random.random(frame.shape[:2]) < intensity

        result = frame.copy()
        for c in range(frame.shape[2]):
            channel_mask = mask
            result[:, :, c] = np.where(channel_mask,
                                     np.clip(frame[:, :, c].astype(np.int16) + noise[:, :, c], 0, 255),
                                     frame[:, :, c])

        return result.astype(np.uint8)

    def apply_scan_lines(self, frame: np.ndarray, line_spacing: int = 3,
                        intensity: float = 0.5) -> np.ndarray:
        """
        Apply scan line effect.

        Args:
            frame: Input frame
            line_spacing: Spacing between scan lines
            intensity: Intensity of scan lines

        Returns:
            Frame with scan lines
        """
        result = frame.copy().astype(np.float32)
        height = frame.shape[0]

        # Create scan line pattern
        for y in range(0, height, line_spacing):
            if y < height:
                result[y, :] *= (1 - intensity)

        return np.clip(result, 0, 255).astype(np.uint8)

    def apply_pixel_sorting(self, frame: np.ndarray, direction: str = "horizontal",
                          threshold: int = 100) -> np.ndarray:
        """
        Apply pixel sorting glitch effect.

        Args:
            frame: Input frame
            direction: "horizontal" or "vertical"
            threshold: Brightness threshold for sorting

        Returns:
            Frame with pixel sorting effect
        """
        result = frame.copy()

        if direction == "horizontal":
            for y in range(frame.shape[0]):
                row = result[y, :]
                # Find bright regions
                brightness = np.mean(row, axis=1)
                bright_mask = brightness > threshold

                if np.any(bright_mask):
                    # Sort pixels in bright regions
                    bright_pixels = row[bright_mask]
                    sorted_pixels = bright_pixels[np.argsort(np.mean(bright_pixels, axis=1))]
                    result[y, bright_mask] = sorted_pixels

        elif direction == "vertical":
            for x in range(frame.shape[1]):
                col = result[:, x]
                # Find bright regions
                brightness = np.mean(col, axis=1)
                bright_mask = brightness > threshold

                if np.any(bright_mask):
                    # Sort pixels in bright regions
                    bright_pixels = col[bright_mask]
                    sorted_pixels = bright_pixels[np.argsort(np.mean(bright_pixels, axis=1))]
                    result[bright_mask, x] = sorted_pixels

        return result

    def apply_rgb_shift(self, frame: np.ndarray, shift_amount: int = 5) -> np.ndarray:
        """
        Apply RGB channel shift glitch.

        Args:
            frame: Input frame
            shift_amount: Amount to shift channels

        Returns:
            Frame with RGB shift
        """
        if len(frame.shape) != 3:
            return frame

        result = frame.copy()
        height, width = frame.shape[:2]

        # Shift red channel
        if shift_amount > 0:
            result[:, shift_amount:, 2] = frame[:, :-shift_amount, 2]
            result[:, :shift_amount, 2] = 0

        # Shift blue channel in opposite direction
        if shift_amount > 0:
            result[:, :-shift_amount, 0] = frame[:, shift_amount:, 0]
            result[:, -shift_amount:, 0] = 0

        return result

    def apply_block_corruption(self, frame: np.ndarray, block_size: int = 16,
                             corruption_rate: float = 0.1) -> np.ndarray:
        """
        Apply block corruption effect.

        Args:
            frame: Input frame
            block_size: Size of corruption blocks
            corruption_rate: Rate of block corruption

        Returns:
            Frame with block corruption
        """
        result = frame.copy()
        height, width = frame.shape[:2]

        # Divide frame into blocks
        for y in range(0, height, block_size):
            for x in range(0, width, block_size):
                if random.random() < corruption_rate:
                    # Corrupt this block
                    y2 = min(y + block_size, height)
                    x2 = min(x + block_size, width)

                    # Apply random corruption
                    corruption_type = random.choice(['noise', 'solid', 'duplicate', 'invert'])

                    if corruption_type == 'noise':
                        result[y:y2, x:x2] = np.random.randint(0, 256, (y2-y, x2-x, frame.shape[2]), dtype=np.uint8)
                    elif corruption_type == 'solid':
                        color = np.random.randint(0, 256, 3)
                        result[y:y2, x:x2] = color
                    elif corruption_type == 'duplicate':
                        # Duplicate a random block
                        src_y = random.randint(0, max(0, height - block_size))
                        src_x = random.randint(0, max(0, width - block_size))
                        src_y2 = min(src_y + block_size, height)
                        src_x2 = min(src_x + block_size, width)

                        # Ensure dimensions match
                        src_block = frame[src_y:src_y2, src_x:src_x2]
                        if src_block.shape[:2] == (y2-y, x2-x):
                            result[y:y2, x:x2] = src_block
                        else:
                            # Resize if dimensions don't match
                            resized_block = cv2.resize(src_block, (x2-x, y2-y))
                            result[y:y2, x:x2] = resized_block
                    elif corruption_type == 'invert':
                        result[y:y2, x:x2] = 255 - frame[y:y2, x:x2]

        return result

    def apply_compression_artifacts(self, frame: np.ndarray, quality: int = 10) -> np.ndarray:
        """
        Apply compression artifacts.

        Args:
            frame: Input frame
            quality: JPEG quality (lower = more artifacts)

        Returns:
            Frame with compression artifacts
        """
        # Encode and decode with low quality JPEG
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
        _, encoded_img = cv2.imencode('.jpg', frame, encode_param)
        decoded_img = cv2.imdecode(encoded_img, cv2.IMREAD_COLOR)

        return decoded_img if decoded_img is not None else frame

    def apply_bit_crush(self, frame: np.ndarray, bits: int = 4) -> np.ndarray:
        """
        Apply bit crushing effect.

        Args:
            frame: Input frame
            bits: Number of bits per channel

        Returns:
            Frame with bit crushing
        """
        # Calculate step size
        step = 256 // (2 ** bits)

        # Quantize
        result = (frame // step) * step

        return result.astype(np.uint8)

    def apply_interlacing(self, frame: np.ndarray, field: str = "odd") -> np.ndarray:
        """
        Apply interlacing effect.

        Args:
            frame: Input frame
            field: "odd" or "even" field to keep

        Returns:
            Frame with interlacing
        """
        result = frame.copy()

        if field == "odd":
            # Keep odd lines, duplicate them to even lines
            result[1::2] = result[::2][:-1] if result.shape[0] % 2 == 0 else result[::2]
        else:
            # Keep even lines, duplicate them to odd lines
            result[::2] = result[1::2]

        return result

    def apply_vhs_artifacts(self, frame: np.ndarray) -> np.ndarray:
        """
        Apply VHS-style artifacts.

        Args:
            frame: Input frame

        Returns:
            Frame with VHS artifacts
        """
        result = frame.copy().astype(np.float32)

        # Add horizontal noise lines
        height = frame.shape[0]
        for _ in range(random.randint(1, 5)):
            y = random.randint(0, height - 1)
            noise_intensity = random.uniform(0.3, 0.8)
            result[y, :] = result[y, :] * (1 - noise_intensity) + 255 * noise_intensity

        # Add color bleeding
        if len(frame.shape) == 3:
            # Slight horizontal blur for color bleeding
            kernel = np.array([[0.1, 0.8, 0.1]])
            for c in range(3):
                result[:, :, c] = cv2.filter2D(result[:, :, c], -1, kernel)

        # Add slight vertical jitter
        if random.random() < 0.3:
            shift = random.randint(-2, 2)
            if shift != 0:
                if shift > 0:
                    result[shift:, :] = result[:-shift, :]
                    result[:shift, :] = 0
                else:
                    result[:shift, :] = result[-shift:, :]
                    result[shift:, :] = 0

        return np.clip(result, 0, 255).astype(np.uint8)

    def apply_digital_decay(self, frames: List[np.ndarray], decay_rate: float = 0.02) -> List[np.ndarray]:
        """
        Apply digital decay effect across frames.

        Args:
            frames: List of input frames
            decay_rate: Rate of digital decay

        Returns:
            List of frames with digital decay
        """
        if not frames:
            return frames

        decayed_frames = []
        accumulated_decay = np.zeros_like(frames[0], dtype=np.float32)

        for frame in frames:
            # Add random decay
            decay_mask = np.random.random(frame.shape[:2]) < decay_rate
            decay_noise = np.random.randint(0, 256, frame.shape, dtype=np.uint8)

            # Accumulate decay
            for c in range(frame.shape[2]):
                accumulated_decay[:, :, c] += decay_mask * decay_noise[:, :, c] * 0.1

            # Apply decay to frame
            result = frame.copy().astype(np.float32)
            result = np.clip(result + accumulated_decay, 0, 255)

            decayed_frames.append(result.astype(np.uint8))

        return decayed_frames
