"""
Content-aware LLM film planner that analyzes video content first, then creates intelligent plans.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging
import base64
from dotenv import load_dotenv
from google import genai
import random

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContentAwarePlanner:
    """LLM planner that analyzes video content first, then creates intelligent plans."""
    
    def __init__(self):
        self.client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Initialize effects
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        
        self.color_fx = ColorEffects()
        self.warp_fx = FrameWarpingEffect()
        self.glitch_fx = GlitchEffects()
        self.datamosh_fx = DatamoshEffect()
    
    def analyze_video_content(self, video_files: list) -> dict:
        """Analyze the content of available videos using LLM vision."""
        
        logger.info(f"🔍 Analyzing content of {len(video_files)} videos...")
        
        video_analysis = {}
        
        for i, video_file in enumerate(video_files):
            logger.info(f"Analyzing video {i+1}: {video_file.name}")
            
            try:
                # Extract representative frames from different parts of the video
                cap = cv2.VideoCapture(str(video_file))
                total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                fps = cap.get(cv2.CAP_PROP_FPS)
                duration = total_frames / fps if fps > 0 else 0
                
                # Extract frames from beginning, middle, and end
                sample_times = [0.1 * duration, 0.5 * duration, 0.9 * duration]
                frames = []
                
                for time_point in sample_times:
                    frame_number = int(time_point * fps)
                    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
                    ret, frame = cap.read()
                    if ret:
                        frames.append((time_point, frame))
                
                cap.release()
                
                if not frames:
                    logger.warning(f"No frames extracted from {video_file.name}")
                    continue
                
                # Analyze frames with LLM
                analysis = self._analyze_frames_with_llm(frames, video_file.name, duration)
                
                video_analysis[str(video_file)] = {
                    "filename": video_file.name,
                    "duration": duration,
                    "analysis": analysis,
                    "sample_times": [t for t, _ in frames]
                }
                
                logger.info(f"✅ Analyzed {video_file.name}: {analysis.get('summary', 'No summary')[:100]}...")
                
            except Exception as e:
                logger.error(f"Error analyzing {video_file.name}: {e}")
                continue
        
        return video_analysis
    
    def _analyze_frames_with_llm(self, frames: list, filename: str, duration: float) -> dict:
        """Analyze frames using LLM vision."""
        
        try:
            # Convert first frame to base64 for LLM analysis
            _, frame = frames[0]
            
            # Resize frame for faster processing
            frame_small = cv2.resize(frame, (512, 384))
            
            # Convert to RGB and then to base64
            frame_rgb = cv2.cvtColor(frame_small, cv2.COLOR_BGR2RGB)
            _, buffer = cv2.imencode('.jpg', frame_rgb)
            frame_b64 = base64.b64encode(buffer).decode('utf-8')
            
            prompt = f"""Analyze this video frame from "{filename}" (duration: {duration:.1f}s).

Describe:
1. SETTING: Where does this take place? (indoor/outdoor, urban/nature, etc.)
2. SUBJECTS: What/who is in the scene? (people, objects, animals, etc.)
3. MOOD: What's the emotional tone? (calm, energetic, dramatic, etc.)
4. VISUAL STYLE: How does it look? (bright/dark, colorful/muted, etc.)
5. MOVEMENT: What kind of motion would you expect? (static, slow, fast, chaotic)
6. ARTISTIC POTENTIAL: What experimental effects would work well with this content?

Be specific and concise. Focus on visual elements that would be relevant for experimental filmmaking."""
            
            # Create the content with image
            content = [
                {
                    "text": prompt
                },
                {
                    "inline_data": {
                        "mime_type": "image/jpeg",
                        "data": frame_b64
                    }
                }
            ]
            
            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=content
            )
            
            analysis_text = response.text
            
            # Parse the analysis into structured data
            analysis = self._parse_content_analysis(analysis_text)
            analysis["raw_analysis"] = analysis_text
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error in LLM frame analysis: {e}")
            return {
                "setting": "unknown",
                "subjects": "unknown", 
                "mood": "neutral",
                "visual_style": "standard",
                "movement": "moderate",
                "artistic_potential": "general effects",
                "summary": "Analysis failed"
            }
    
    def _parse_content_analysis(self, analysis_text: str) -> dict:
        """Parse LLM analysis into structured data."""
        
        analysis = {}
        lines = analysis_text.split('\n')
        
        current_key = None
        current_value = []
        
        for line in lines:
            line = line.strip()
            
            # Look for section headers
            if any(keyword in line.upper() for keyword in ['SETTING:', 'SUBJECTS:', 'MOOD:', 'VISUAL STYLE:', 'MOVEMENT:', 'ARTISTIC POTENTIAL:']):
                # Save previous section
                if current_key and current_value:
                    analysis[current_key] = ' '.join(current_value).strip()
                
                # Start new section
                if 'SETTING:' in line.upper():
                    current_key = 'setting'
                elif 'SUBJECTS:' in line.upper():
                    current_key = 'subjects'
                elif 'MOOD:' in line.upper():
                    current_key = 'mood'
                elif 'VISUAL STYLE:' in line.upper():
                    current_key = 'visual_style'
                elif 'MOVEMENT:' in line.upper():
                    current_key = 'movement'
                elif 'ARTISTIC POTENTIAL:' in line.upper():
                    current_key = 'artistic_potential'
                
                # Get content after colon
                if ':' in line:
                    current_value = [line.split(':', 1)[1].strip()]
                else:
                    current_value = []
            
            elif current_key and line:
                current_value.append(line)
        
        # Save final section
        if current_key and current_value:
            analysis[current_key] = ' '.join(current_value).strip()
        
        # Create summary
        analysis['summary'] = f"{analysis.get('setting', 'Unknown setting')} with {analysis.get('subjects', 'unknown subjects')}, {analysis.get('mood', 'neutral')} mood"
        
        return analysis
    
    def create_content_aware_plan(self, concept: str, style: str, video_analysis: dict) -> dict:
        """Create a film plan based on concept AND available video content."""
        
        logger.info(f"🎬 Creating content-aware plan for '{concept}' ({style})")
        
        # Create content summary for LLM
        content_summary = "AVAILABLE VIDEO CONTENT:\n"
        for i, (video_path, analysis) in enumerate(video_analysis.items(), 1):
            filename = analysis['filename']
            duration = analysis['analysis'].get('duration', 0)
            summary = analysis['analysis'].get('summary', 'No analysis')
            setting = analysis['analysis'].get('setting', 'Unknown')
            mood = analysis['analysis'].get('mood', 'Neutral')
            
            content_summary += f"\nVIDEO {i}: {filename} ({duration:.1f}s)\n"
            content_summary += f"  Content: {summary}\n"
            content_summary += f"  Setting: {setting}\n"
            content_summary += f"  Mood: {mood}\n"
        
        prompt = f"""Create an experimental film plan for the concept: "{concept}" with style: "{style}"

{content_summary}

You are an experimental filmmaker with access to the above video content. Create a detailed plan that:

1. MATCHES CONTENT TO CONCEPT: Choose specific videos that best represent different aspects of "{concept}"
2. CREATES NARRATIVE ARC: Use the available content to tell a story about "{concept}"
3. SELECTS APPROPRIATE EFFECTS: Match effects to both the concept AND the specific video content

For each segment, specify:
- Which video to use (VIDEO 1, VIDEO 2, etc.)
- Start and end time within that video
- Why this content fits the concept
- Which effect to apply and why
- Duration for the segment

Available effects:
- color_bleed: Separates color channels (intensity 0.1-1.0)
- chromatic_aberration: Lens-like color separation (intensity 0.1-1.0)
- wave_distortion: Flowing liquid effects (amplitude 10-50)
- spiral_distortion: Hypnotic swirling (intensity 0.1-1.0)
- kaleidoscope: Symmetrical patterns (segments 3-12)
- digital_noise: Glitch corruption (intensity 0.1-1.0)
- rgb_shift: Channel displacement (shift_amount 1-20)
- scan_lines: TV-like artifacts (intensity 0.1-1.0)
- datamosh: Motion corruption (intensity 0.1-1.0)

Create 4-6 segments that total 20-25 seconds and tell a coherent story about "{concept}" using the available content.

Format:
SEGMENT 1: [concept aspect] using [VIDEO X] ([start]s-[end]s)
Reason: [why this content fits]
Effect: [effect_name]
Parameters: [parameters]
Duration: [seconds]

SEGMENT 2: ...
"""
        
        try:
            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=prompt
            )
            
            plan_text = response.text
            logger.info(f"Content-aware plan created:\n{plan_text}")
            
            # Parse the plan
            segments = self._parse_content_aware_plan(plan_text, video_analysis)
            
            return {
                "concept": concept,
                "style": style,
                "plan_text": plan_text,
                "segments": segments,
                "content_analysis": video_analysis
            }
            
        except Exception as e:
            logger.error(f"Error creating content-aware plan: {e}")
            return {"concept": concept, "style": style, "segments": [], "error": str(e)}
    
    def _parse_content_aware_plan(self, plan_text: str, video_analysis: dict) -> list:
        """Parse content-aware plan into structured segments."""
        
        segments = []
        lines = plan_text.split('\n')
        current_segment = None
        
        # Create video path lookup
        video_paths = list(video_analysis.keys())
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('SEGMENT'):
                if current_segment:
                    segments.append(current_segment)
                
                # Parse segment header
                current_segment = {
                    "description": line,
                    "video_path": None,
                    "start_time": 0,
                    "end_time": 5,
                    "effect": None,
                    "parameters": {},
                    "duration": 5.0,
                    "reason": ""
                }
                
                # Extract video number and times from segment line
                import re
                video_match = re.search(r'VIDEO (\d+)', line.upper())
                if video_match:
                    video_num = int(video_match.group(1)) - 1
                    if 0 <= video_num < len(video_paths):
                        current_segment["video_path"] = video_paths[video_num]
                
                time_match = re.search(r'(\d+(?:\.\d+)?)s-(\d+(?:\.\d+)?)s', line)
                if time_match:
                    current_segment["start_time"] = float(time_match.group(1))
                    current_segment["end_time"] = float(time_match.group(2))
            
            elif line.startswith('Reason:') and current_segment:
                current_segment["reason"] = line.split(':', 1)[1].strip()
            
            elif line.startswith('Effect:') and current_segment:
                effect = line.split(':', 1)[1].strip()
                current_segment["effect"] = effect
            
            elif line.startswith('Parameters:') and current_segment:
                params_text = line.split(':', 1)[1].strip()
                current_segment["parameters"] = self._parse_parameters(params_text)
            
            elif line.startswith('Duration:') and current_segment:
                duration_text = line.split(':', 1)[1].strip()
                try:
                    import re
                    match = re.search(r'([0-9.]+)', duration_text)
                    if match:
                        current_segment["duration"] = float(match.group(1))
                except:
                    current_segment["duration"] = 5.0
        
        # Add final segment
        if current_segment:
            segments.append(current_segment)
        
        return segments
    
    def _parse_parameters(self, params_text: str) -> dict:
        """Parse parameter text into dictionary."""
        
        params = {}
        
        if "intensity" in params_text.lower():
            try:
                import re
                match = re.search(r'intensity[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["intensity"] = float(match.group(1))
            except:
                params["intensity"] = 0.5
        
        if "amplitude" in params_text.lower():
            try:
                match = re.search(r'amplitude[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["amplitude"] = float(match.group(1))
            except:
                params["amplitude"] = 20
        
        if "segments" in params_text.lower():
            try:
                match = re.search(r'segments[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["segments"] = int(match.group(1))
            except:
                params["segments"] = 6
        
        if "shift_amount" in params_text.lower():
            try:
                match = re.search(r'shift_amount[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["shift_amount"] = int(match.group(1))
            except:
                params["shift_amount"] = 5
        
        return params

    def execute_content_aware_plan(self, plan: dict) -> list:
        """Execute the content-aware plan using specific video segments."""

        if not plan.get("segments"):
            logger.warning("No segments to execute")
            return []

        from video_analysis.frame_extractor import FrameExtractor
        extractor = FrameExtractor()

        all_processed_frames = []
        fps = 24

        logger.info(f"Executing content-aware plan with {len(plan['segments'])} segments")

        for i, segment in enumerate(plan["segments"]):
            video_path = segment.get("video_path")
            start_time = segment.get("start_time", 0)
            end_time = segment.get("end_time", 5)
            effect = segment.get("effect", "").lower()
            params = segment.get("parameters", {})
            duration = segment.get("duration", 5.0)
            reason = segment.get("reason", "")

            logger.info(f"\nSegment {i+1}: {segment['description']}")
            logger.info(f"  Video: {Path(video_path).name if video_path else 'Unknown'}")
            logger.info(f"  Time: {start_time:.1f}s - {end_time:.1f}s")
            logger.info(f"  Reason: {reason}")
            logger.info(f"  Effect: {effect}")
            logger.info(f"  Parameters: {params}")

            if not video_path or not Path(video_path).exists():
                logger.error(f"Video path not found: {video_path}")
                continue

            try:
                # Extract frames from specific time range
                frames = extractor.extract_frames(Path(video_path), start_time=start_time, end_time=end_time)

                if not frames:
                    logger.warning(f"No frames extracted from {video_path}")
                    continue

                # Convert to frame list
                frame_list = [frame for _, frame in frames]

                # Calculate frames needed for target duration
                frames_needed = int(duration * fps)

                # Adjust frame count
                if len(frame_list) > frames_needed:
                    # Subsample frames
                    indices = np.linspace(0, len(frame_list) - 1, frames_needed, dtype=int)
                    frame_list = [frame_list[i] for i in indices]
                elif len(frame_list) < frames_needed:
                    # Repeat frames to fill duration
                    while len(frame_list) < frames_needed:
                        frame_list.extend(frame_list[:min(len(frame_list), frames_needed - len(frame_list))])
                    frame_list = frame_list[:frames_needed]

                # Apply the effect
                processed_frames = self._apply_effect(frame_list, effect, params)

                # Add to final video
                all_processed_frames.extend(processed_frames)

                logger.info(f"  ✅ Added {len(processed_frames)} frames")

            except Exception as e:
                logger.error(f"  ❌ Error processing segment: {e}")

        logger.info(f"Total frames: {len(all_processed_frames)} ({len(all_processed_frames)/fps:.1f}s)")
        return all_processed_frames

    def _apply_effect(self, frames: list, effect: str, params: dict) -> list:
        """Apply effect to frames."""

        try:
            if "color_bleed" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.color_fx.apply_color_bleed(f, intensity) for f in frames]

            elif "chromatic_aberration" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.color_fx.apply_chromatic_aberration(f, intensity) for f in frames]

            elif "wave_distortion" in effect:
                amplitude = params.get("amplitude", 20)
                return [self.warp_fx.apply_wave_distortion(f, amplitude) for f in frames]

            elif "spiral_distortion" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.warp_fx.apply_spiral_distortion(f, intensity) for f in frames]

            elif "kaleidoscope" in effect:
                segments = params.get("segments", 6)
                return [self.warp_fx.apply_kaleidoscope(f, segments) for f in frames]

            elif "digital_noise" in effect:
                intensity = params.get("intensity", 0.3)
                return [self.glitch_fx.apply_digital_noise(f, intensity) for f in frames]

            elif "rgb_shift" in effect:
                shift_amount = params.get("shift_amount", 5)
                return [self.glitch_fx.apply_rgb_shift(f, shift_amount) for f in frames]

            elif "scan_lines" in effect:
                intensity = params.get("intensity", 0.5)
                return [self.glitch_fx.apply_scan_lines(f, intensity=intensity) for f in frames]

            elif "datamosh" in effect:
                intensity = params.get("intensity", 0.5)
                return self.datamosh_fx.apply_datamosh(frames, intensity)

            else:
                logger.warning(f"Unknown effect: {effect}")
                return frames

        except Exception as e:
            logger.error(f"Error applying {effect}: {e}")
            return frames

def test_content_aware_planner():
    """Test the content-aware planner."""

    logger.info("🎬 Testing Content-Aware LLM Planner")

    try:
        # Initialize planner
        planner = ContentAwarePlanner()

        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))[:3]  # Use first 3 videos

        if len(video_files) < 2:
            logger.error("Need at least 2 video files")
            return False

        logger.info(f"Found {len(video_files)} videos for analysis")

        # Step 1: Analyze video content
        video_analysis = planner.analyze_video_content(video_files)

        if not video_analysis:
            logger.error("No video analysis completed")
            return False

        # Step 2: Create content-aware plans
        concepts = [
            ("urban isolation", "melancholic"),
            ("digital transformation", "surreal")
        ]

        for concept, style in concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎭 Creating content-aware plan for: '{concept}' ({style})")
            logger.info(f"{'='*60}")

            # Create plan based on actual content
            plan = planner.create_content_aware_plan(concept, style, video_analysis)

            if plan.get("error"):
                logger.error(f"Plan creation failed: {plan['error']}")
                continue

            # Execute the content-aware plan
            processed_frames = planner.execute_content_aware_plan(plan)

            if not processed_frames:
                logger.error("No frames generated")
                continue

            # Save result
            output_path = Path(f"./output/content_aware_{concept.replace(' ', '_')}.mp4")
            output_path.parent.mkdir(exist_ok=True)

            # Save as video
            if save_frames_as_video(processed_frames, output_path):
                file_size = output_path.stat().st_size / 1024 / 1024
                duration = len(processed_frames) / 24
                logger.info(f"✅ Created content-aware film: {output_path}")
                logger.info(f"   Duration: {duration:.1f}s, Size: {file_size:.1f}MB")
            else:
                logger.error(f"❌ Failed to save: {output_path}")

        return True

    except Exception as e:
        logger.error(f"Error in content-aware planner test: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_frames_as_video(frames: list, output_path: Path, fps: int = 24) -> bool:
    """Save frames as video."""

    if not frames:
        return False

    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

        if not out.isOpened():
            return False

        for frame in frames:
            frame_resized = cv2.resize(frame, (width, height))
            out.write(frame_resized)

        out.release()
        return output_path.exists()

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False

if __name__ == "__main__":
    test_content_aware_planner()
