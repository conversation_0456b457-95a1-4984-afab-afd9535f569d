"""
LLM-driven film planning for experimental video creation.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json
from openai import OpenAI
from google import genai

from config import config

logger = logging.getLogger(__name__)


@dataclass
class FilmSegment:
    """Represents a planned film segment."""
    description: str
    search_query: str
    duration: float
    effects: List[str]
    transition: str
    mood: str
    style_notes: str


@dataclass
class FilmPlan:
    """Represents a complete film plan."""
    concept: str
    total_duration: float
    style: str
    narrative_structure: str
    segments: List[FilmSegment]
    overall_effects: List[str]
    audio_notes: str


class FilmPlanner:
    """Creates experimental film plans using LLM reasoning."""
    
    def __init__(self, provider: str = None):
        self.provider = provider or config.llm.default_provider
        
        if self.provider == "openai":
            self.openai_client = OpenAI(api_key=config.llm.openai_api_key)
        elif self.provider == "gemini":
            self.gemini_client = genai.Client(api_key=config.llm.gemini_api_key)
        else:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")
    
    def create_plan(self, concept: str, duration: float = 60.0, style: str = None) -> FilmPlan:
        """
        Create a film plan based on a concept.
        
        Args:
            concept: The creative concept or theme
            duration: Target duration in seconds
            style: Optional style preference
            
        Returns:
            FilmPlan object
        """
        logger.info(f"Creating film plan for concept: {concept}")
        
        if self.provider == "openai":
            return self._plan_with_openai(concept, duration, style)
        elif self.provider == "gemini":
            return self._plan_with_gemini(concept, duration, style)
    
    def _plan_with_openai(self, concept: str, duration: float, style: str) -> FilmPlan:
        """Create film plan using OpenAI."""
        
        system_prompt = """You are an experimental filmmaker and creative director. 
        Create detailed plans for experimental short films that combine found footage with artistic effects.
        
        Your plans should be:
        - Conceptually coherent but artistically bold
        - Technically feasible with video editing tools
        - Emotionally engaging
        - Visually striking
        
        Consider experimental techniques like:
        - Datamoshing and glitch effects
        - Color manipulation and bleeding
        - Frame warping and distortion
        - Recursive visual loops
        - Juxtaposition of contrasting imagery
        - Non-linear narrative structures"""
        
        user_prompt = f"""Create an experimental film plan for the concept: "{concept}"
        
        Target duration: {duration} seconds
        Style preference: {style or "experimental/avant-garde"}
        
        Provide a detailed plan including:
        1. Overall narrative structure and flow
        2. 5-8 specific segments with descriptions
        3. Search queries to find matching footage
        4. Specific effects for each segment
        5. Transitions between segments
        6. Overall mood and aesthetic notes
        
        Format your response as a structured plan that can guide the creation process."""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=config.llm.max_tokens,
                temperature=0.8  # Higher temperature for creativity
            )
            
            plan_text = response.choices[0].message.content
            return self._parse_plan_response(plan_text, concept, duration, style)
            
        except Exception as e:
            logger.error(f"OpenAI planning failed: {e}")
            return self._create_fallback_plan(concept, duration, style)
    
    def _plan_with_gemini(self, concept: str, duration: float, style: str) -> FilmPlan:
        """Create film plan using Gemini."""
        
        prompt = f"""Create an experimental film plan for the concept: "{concept}"

        Target duration: {duration} seconds
        Style: {style or "experimental/avant-garde"}
        
        As an experimental filmmaker, create a detailed plan that includes:
        1. Overall narrative structure
        2. 5-8 specific segments with descriptions
        3. Search queries to find matching footage
        4. Experimental effects for each segment
        5. Transitions between segments
        6. Mood and aesthetic notes
        
        Focus on creating something visually striking and conceptually coherent."""
        
        try:
            response = self.gemini_client.models.generate_content(
                model=config.llm.gemini_model,
                contents=[prompt]
            )
            
            plan_text = response.text
            return self._parse_plan_response(plan_text, concept, duration, style)
            
        except Exception as e:
            logger.error(f"Gemini planning failed: {e}")
            return self._create_fallback_plan(concept, duration, style)
    
    def _parse_plan_response(self, plan_text: str, concept: str, duration: float, style: str) -> FilmPlan:
        """Parse LLM response into structured FilmPlan."""
        
        # This is a simplified parser - in production, you might want more sophisticated parsing
        # or ask the LLM to return structured JSON
        
        segments = []
        segment_duration = duration / 6  # Default to 6 segments
        
        # Extract segments from the plan text
        # This is a basic implementation - could be enhanced with better parsing
        lines = plan_text.split('\n')
        current_segment = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Look for segment indicators
            if any(keyword in line.lower() for keyword in ['segment', 'scene', 'part', 'section']):
                if current_segment:
                    segments.append(current_segment)
                
                # Create new segment
                current_segment = FilmSegment(
                    description=line,
                    search_query=self._extract_search_terms(line),
                    duration=segment_duration,
                    effects=self._suggest_effects_for_segment(line),
                    transition="fade",
                    mood="experimental",
                    style_notes=""
                )
            elif current_segment and line:
                # Add to current segment description
                current_segment.description += " " + line
                current_segment.search_query = self._extract_search_terms(current_segment.description)
        
        # Add final segment
        if current_segment:
            segments.append(current_segment)
        
        # If no segments were parsed, create default ones
        if not segments:
            segments = self._create_default_segments(concept, duration)
        
        return FilmPlan(
            concept=concept,
            total_duration=duration,
            style=style or "experimental",
            narrative_structure="Non-linear experimental narrative",
            segments=segments,
            overall_effects=["color_grading", "audio_sync"],
            audio_notes="Ambient experimental soundtrack"
        )
    
    def _extract_search_terms(self, description: str) -> str:
        """Extract search terms from segment description."""
        # Simple keyword extraction - could be enhanced with NLP
        keywords = []
        
        # Common visual elements
        visual_terms = ['urban', 'nature', 'people', 'movement', 'static', 'close-up', 'wide shot', 
                       'indoor', 'outdoor', 'night', 'day', 'water', 'fire', 'crowd', 'empty']
        
        for term in visual_terms:
            if term in description.lower():
                keywords.append(term)
        
        return " ".join(keywords) if keywords else description[:50]
    
    def _suggest_effects_for_segment(self, description: str) -> List[str]:
        """Suggest effects based on segment description."""
        effects = []
        
        desc_lower = description.lower()
        
        if any(word in desc_lower for word in ['glitch', 'digital', 'error']):
            effects.extend(['datamosh', 'glitch'])
        
        if any(word in desc_lower for word in ['dream', 'surreal', 'abstract']):
            effects.extend(['color_bleed', 'frame_warp'])
        
        if any(word in desc_lower for word in ['intense', 'dramatic', 'chaos']):
            effects.extend(['high_contrast', 'rapid_cuts'])
        
        if any(word in desc_lower for word in ['calm', 'peaceful', 'slow']):
            effects.extend(['slow_motion', 'soft_blur'])
        
        # Default effects if none matched
        if not effects:
            effects = ['color_grade', 'subtle_warp']
        
        return effects
    
    def _create_default_segments(self, concept: str, duration: float) -> List[FilmSegment]:
        """Create default segments if parsing fails."""
        segment_duration = duration / 5
        
        return [
            FilmSegment(
                description=f"Opening: Establishing the {concept} theme",
                search_query=concept,
                duration=segment_duration,
                effects=['fade_in', 'color_grade'],
                transition="fade",
                mood="mysterious",
                style_notes="Slow build-up"
            ),
            FilmSegment(
                description=f"Development: Exploring {concept} visually",
                search_query=f"{concept} visual exploration",
                duration=segment_duration,
                effects=['datamosh', 'color_shift'],
                transition="cut",
                mood="experimental",
                style_notes="Increasing intensity"
            ),
            FilmSegment(
                description=f"Climax: Peak expression of {concept}",
                search_query=f"{concept} intense dramatic",
                duration=segment_duration,
                effects=['glitch', 'frame_warp'],
                transition="smash_cut",
                mood="intense",
                style_notes="Maximum experimental effects"
            ),
            FilmSegment(
                description=f"Resolution: Reflecting on {concept}",
                search_query=f"{concept} contemplative peaceful",
                duration=segment_duration,
                effects=['slow_motion', 'soft_blur'],
                transition="dissolve",
                mood="contemplative",
                style_notes="Calming down"
            ),
            FilmSegment(
                description=f"Ending: Final statement on {concept}",
                search_query=f"{concept} conclusion",
                duration=segment_duration,
                effects=['fade_out'],
                transition="fade",
                mood="conclusive",
                style_notes="Strong ending"
            )
        ]
    
    def _create_fallback_plan(self, concept: str, duration: float, style: str) -> FilmPlan:
        """Create a fallback plan if LLM planning fails."""
        logger.warning("Using fallback film plan")
        
        return FilmPlan(
            concept=concept,
            total_duration=duration,
            style=style or "experimental",
            narrative_structure="Simple experimental structure",
            segments=self._create_default_segments(concept, duration),
            overall_effects=["color_grading"],
            audio_notes="Ambient soundtrack"
        )
