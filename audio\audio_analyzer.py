"""
Audio analysis for synchronizing with video content.
"""

import logging
import numpy as np
import librosa
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional

logger = logging.getLogger(__name__)


class AudioAnalyzer:
    """Analyzes audio content for synchronization with video effects."""
    
    def __init__(self, sample_rate: int = 44100):
        self.sample_rate = sample_rate
    
    def analyze_audio_features(self, audio: np.ndarray) -> Dict[str, Any]:
        """
        Analyze audio features.
        
        Args:
            audio: Audio data
            
        Returns:
            Dictionary of audio features
        """
        try:
            features = {}
            
            # Basic features
            features['duration'] = len(audio) / self.sample_rate
            features['rms_energy'] = np.sqrt(np.mean(audio**2))
            features['zero_crossing_rate'] = np.mean(librosa.feature.zero_crossing_rate(audio))
            
            # Spectral features
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=self.sample_rate)[0]
            features['spectral_centroid_mean'] = np.mean(spectral_centroids)
            features['spectral_centroid_std'] = np.std(spectral_centroids)
            
            spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=self.sample_rate)[0]
            features['spectral_rolloff_mean'] = np.mean(spectral_rolloff)
            
            # MFCC features
            mfccs = librosa.feature.mfcc(y=audio, sr=self.sample_rate, n_mfcc=13)
            features['mfcc_mean'] = np.mean(mfccs, axis=1).tolist()
            features['mfcc_std'] = np.std(mfccs, axis=1).tolist()
            
            # Tempo and beat tracking
            tempo, beats = librosa.beat.beat_track(y=audio, sr=self.sample_rate)
            features['tempo'] = float(tempo)
            features['beat_times'] = librosa.frames_to_time(beats, sr=self.sample_rate).tolist()
            
            # Onset detection
            onset_frames = librosa.onset.onset_detect(y=audio, sr=self.sample_rate)
            features['onset_times'] = librosa.frames_to_time(onset_frames, sr=self.sample_rate).tolist()
            
            return features
            
        except Exception as e:
            logger.error(f"Error analyzing audio features: {e}")
            return {}
    
    def detect_audio_events(self, audio: np.ndarray, threshold: float = 0.1) -> List[Dict[str, Any]]:
        """
        Detect significant audio events.
        
        Args:
            audio: Audio data
            threshold: Detection threshold
            
        Returns:
            List of audio events
        """
        events = []
        
        try:
            # Onset detection
            onset_frames = librosa.onset.onset_detect(
                y=audio, sr=self.sample_rate, 
                delta=threshold, wait=int(0.1 * self.sample_rate)
            )
            onset_times = librosa.frames_to_time(onset_frames, sr=self.sample_rate)
            
            # Onset strength
            onset_strength = librosa.onset.onset_strength(y=audio, sr=self.sample_rate)
            
            for i, time in enumerate(onset_times):
                if i < len(onset_strength):
                    events.append({
                        'type': 'onset',
                        'time': float(time),
                        'strength': float(onset_strength[onset_frames[i]]),
                        'description': 'Audio onset detected'
                    })
            
            # Beat detection
            tempo, beats = librosa.beat.beat_track(y=audio, sr=self.sample_rate)
            beat_times = librosa.frames_to_time(beats, sr=self.sample_rate)
            
            for time in beat_times:
                events.append({
                    'type': 'beat',
                    'time': float(time),
                    'tempo': float(tempo),
                    'description': 'Beat detected'
                })
            
            # Sort events by time
            events.sort(key=lambda x: x['time'])
            
            return events
            
        except Exception as e:
            logger.error(f"Error detecting audio events: {e}")
            return []
    
    def analyze_audio_mood(self, audio: np.ndarray) -> Dict[str, float]:
        """
        Analyze audio mood characteristics.
        
        Args:
            audio: Audio data
            
        Returns:
            Dictionary of mood characteristics
        """
        try:
            mood = {}
            
            # Energy-based features
            rms_energy = np.sqrt(np.mean(audio**2))
            mood['energy'] = float(rms_energy)
            
            # Spectral features for mood
            spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=audio, sr=self.sample_rate))
            mood['brightness'] = float(spectral_centroid / (self.sample_rate / 2))  # Normalized
            
            # Spectral rolloff for fullness
            spectral_rolloff = np.mean(librosa.feature.spectral_rolloff(y=audio, sr=self.sample_rate))
            mood['fullness'] = float(spectral_rolloff / (self.sample_rate / 2))  # Normalized
            
            # Zero crossing rate for roughness
            zcr = np.mean(librosa.feature.zero_crossing_rate(audio))
            mood['roughness'] = float(zcr)
            
            # Tempo for activity
            tempo, _ = librosa.beat.beat_track(y=audio, sr=self.sample_rate)
            mood['activity'] = float(tempo / 200.0)  # Normalized (200 BPM as reference)
            
            # Spectral contrast for dynamics
            spectral_contrast = librosa.feature.spectral_contrast(y=audio, sr=self.sample_rate)
            mood['dynamics'] = float(np.mean(spectral_contrast))
            
            # Harmonicity (using harmonic-percussive separation)
            harmonic, percussive = librosa.effects.hpss(audio)
            harmonic_energy = np.sqrt(np.mean(harmonic**2))
            percussive_energy = np.sqrt(np.mean(percussive**2))
            
            if harmonic_energy + percussive_energy > 0:
                mood['harmonicity'] = float(harmonic_energy / (harmonic_energy + percussive_energy))
            else:
                mood['harmonicity'] = 0.5
            
            return mood
            
        except Exception as e:
            logger.error(f"Error analyzing audio mood: {e}")
            return {}
    
    def extract_audio_from_video(self, video_path: Path, output_path: Optional[Path] = None) -> Optional[Path]:
        """
        Extract audio from video file.
        
        Args:
            video_path: Path to video file
            output_path: Optional output path for audio
            
        Returns:
            Path to extracted audio file
        """
        try:
            from moviepy.editor import VideoFileClip
            
            if output_path is None:
                output_path = video_path.parent / f"{video_path.stem}_audio.wav"
            
            # Load video and extract audio
            with VideoFileClip(str(video_path)) as video:
                if video.audio is not None:
                    video.audio.write_audiofile(str(output_path), verbose=False, logger=None)
                    return output_path
                else:
                    logger.warning(f"No audio track found in {video_path}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error extracting audio from {video_path}: {e}")
            return None
    
    def create_audio_sync_map(self, audio: np.ndarray, video_duration: float) -> List[Dict[str, Any]]:
        """
        Create synchronization map between audio and video.
        
        Args:
            audio: Audio data
            video_duration: Duration of video in seconds
            
        Returns:
            List of sync points
        """
        sync_map = []
        
        try:
            # Detect audio events
            events = self.detect_audio_events(audio)
            
            # Analyze audio mood over time
            window_size = int(2.0 * self.sample_rate)  # 2-second windows
            hop_size = int(0.5 * self.sample_rate)  # 0.5-second hops
            
            for i in range(0, len(audio) - window_size, hop_size):
                window = audio[i:i + window_size]
                time = i / self.sample_rate
                
                if time > video_duration:
                    break
                
                mood = self.analyze_audio_mood(window)
                
                sync_point = {
                    'time': time,
                    'mood': mood,
                    'events': [e for e in events if abs(e['time'] - time) < 1.0]  # Events within 1 second
                }
                
                sync_map.append(sync_point)
            
            return sync_map
            
        except Exception as e:
            logger.error(f"Error creating audio sync map: {e}")
            return []
    
    def suggest_effect_timing(self, audio: np.ndarray, effect_type: str) -> List[float]:
        """
        Suggest timing for video effects based on audio analysis.
        
        Args:
            audio: Audio data
            effect_type: Type of effect to time
            
        Returns:
            List of suggested timestamps
        """
        timestamps = []
        
        try:
            if effect_type == "glitch":
                # Suggest glitch effects on strong onsets
                onset_frames = librosa.onset.onset_detect(y=audio, sr=self.sample_rate)
                onset_strength = librosa.onset.onset_strength(y=audio, sr=self.sample_rate)
                
                # Get strongest onsets
                strong_onsets = onset_frames[onset_strength[onset_frames] > np.percentile(onset_strength, 80)]
                timestamps = librosa.frames_to_time(strong_onsets, sr=self.sample_rate).tolist()
            
            elif effect_type == "color_shift":
                # Suggest color shifts on beat changes
                tempo, beats = librosa.beat.beat_track(y=audio, sr=self.sample_rate)
                beat_times = librosa.frames_to_time(beats, sr=self.sample_rate)
                
                # Every 4th beat for color shifts
                timestamps = [float(t) for i, t in enumerate(beat_times) if i % 4 == 0]
            
            elif effect_type == "datamosh":
                # Suggest datamosh on low-energy sections
                rms = librosa.feature.rms(y=audio, frame_length=2048, hop_length=512)[0]
                times = librosa.frames_to_time(np.arange(len(rms)), sr=self.sample_rate, hop_length=512)
                
                # Find low-energy regions
                low_energy_threshold = np.percentile(rms, 30)
                low_energy_times = times[rms < low_energy_threshold]
                
                # Sample some of these times
                if len(low_energy_times) > 0:
                    timestamps = np.random.choice(low_energy_times, 
                                                size=min(5, len(low_energy_times)), 
                                                replace=False).tolist()
            
            return timestamps
            
        except Exception as e:
            logger.error(f"Error suggesting effect timing: {e}")
            return []
