"""
Datamoshing effects for experimental video manipulation.
"""

import logging
import numpy as np
import cv2
from pathlib import Path
from typing import Optional, <PERSON><PERSON>
import random

from config import config

logger = logging.getLogger(__name__)


class DatamoshEffect:
    """Implements datamoshing effects by manipulating video frames."""
    
    def __init__(self):
        self.intensity = config.effects.datamosh_intensity
        self.frequency = config.effects.datamosh_frequency
    
    def apply_datamosh(self, frames: list, intensity: float = None) -> list:
        """
        Apply datamoshing effect to a sequence of frames.
        
        Args:
            frames: List of frames as numpy arrays
            intensity: Effect intensity (0.0 to 1.0)
            
        Returns:
            List of datamoshed frames
        """
        if not frames:
            return frames
        
        intensity = intensity or self.intensity
        datamoshed_frames = []
        
        for i, frame in enumerate(frames):
            if i == 0:
                # Keep first frame unchanged
                datamoshed_frames.append(frame.copy())
                continue
            
            # Apply datamosh effect based on frequency
            if i % self.frequency == 0 and random.random() < intensity:
                # Create datamosh by blending with previous frame
                datamoshed_frame = self._create_datamosh_frame(
                    frames[i-1], frame, intensity
                )
                datamoshed_frames.append(datamoshed_frame)
            else:
                datamoshed_frames.append(frame.copy())
        
        return datamoshed_frames
    
    def _create_datamosh_frame(self, prev_frame: np.ndarray, current_frame: np.ndarray, intensity: float) -> np.ndarray:
        """
        Create a datamoshed frame by blending motion vectors.
        
        Args:
            prev_frame: Previous frame
            current_frame: Current frame
            intensity: Datamosh intensity
            
        Returns:
            Datamoshed frame
        """
        # Convert to grayscale for motion detection
        prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
        curr_gray = cv2.cvtColor(current_frame, cv2.COLOR_BGR2GRAY)
        
        # Calculate optical flow
        flow = cv2.calcOpticalFlowPyrLK(
            prev_gray, curr_gray, 
            np.array([[x, y] for y in range(0, prev_gray.shape[0], 20) 
                     for x in range(0, prev_gray.shape[1], 20)], dtype=np.float32),
            None
        )[0]
        
        # Create datamosh effect
        datamoshed = current_frame.copy()
        
        # Apply motion vector corruption
        if flow is not None and len(flow) > 0:
            # Corrupt motion vectors
            corrupted_flow = self._corrupt_motion_vectors(flow, intensity)
            
            # Apply corrupted motion to create datamosh
            datamoshed = self._apply_corrupted_motion(prev_frame, current_frame, corrupted_flow, intensity)
        
        return datamoshed
    
    def _corrupt_motion_vectors(self, flow: np.ndarray, intensity: float) -> np.ndarray:
        """Corrupt motion vectors to create datamosh effect."""
        corrupted = flow.copy()
        
        # Add random noise to motion vectors
        noise_scale = intensity * 50
        noise = np.random.normal(0, noise_scale, flow.shape)
        corrupted += noise
        
        # Randomly duplicate some motion vectors
        if random.random() < intensity:
            duplicate_indices = np.random.choice(len(corrupted), int(len(corrupted) * intensity))
            source_indices = np.random.choice(len(corrupted), len(duplicate_indices))
            corrupted[duplicate_indices] = corrupted[source_indices]
        
        return corrupted
    
    def _apply_corrupted_motion(self, prev_frame: np.ndarray, current_frame: np.ndarray, 
                               corrupted_flow: np.ndarray, intensity: float) -> np.ndarray:
        """Apply corrupted motion vectors to create datamosh effect."""
        
        height, width = current_frame.shape[:2]
        datamoshed = current_frame.copy()
        
        # Create a blend of current frame and motion-corrupted previous frame
        try:
            # Simple approach: blend frames with some corruption
            blend_factor = intensity * 0.5
            
            # Add some pixel displacement based on corrupted flow
            for i in range(0, len(corrupted_flow), 10):  # Sample every 10th point
                if i < len(corrupted_flow):
                    x, y = corrupted_flow[i]
                    
                    # Clamp coordinates
                    x = int(np.clip(x, 0, width - 1))
                    y = int(np.clip(y, 0, height - 1))
                    
                    # Create small corruption patches
                    patch_size = 5
                    y1 = max(0, y - patch_size)
                    y2 = min(height, y + patch_size)
                    x1 = max(0, x - patch_size)
                    x2 = min(width, x + patch_size)
                    
                    if random.random() < intensity:
                        # Copy patch from previous frame
                        datamoshed[y1:y2, x1:x2] = prev_frame[y1:y2, x1:x2]
            
            # Final blend
            datamoshed = cv2.addWeighted(current_frame, 1 - blend_factor, datamoshed, blend_factor, 0)
            
        except Exception as e:
            logger.warning(f"Error applying corrupted motion: {e}")
            # Fallback to simple blend
            datamoshed = cv2.addWeighted(current_frame, 0.7, prev_frame, 0.3, 0)
        
        return datamoshed
    
    def apply_i_frame_corruption(self, frames: list, corruption_rate: float = 0.1) -> list:
        """
        Simulate I-frame corruption for more dramatic datamosh effects.
        
        Args:
            frames: List of frames
            corruption_rate: Rate of I-frame corruption
            
        Returns:
            List of frames with I-frame corruption
        """
        if not frames:
            return frames
        
        corrupted_frames = []
        reference_frame = frames[0].copy()
        
        for i, frame in enumerate(frames):
            if i == 0 or random.random() > corruption_rate:
                # Normal frame or new reference
                corrupted_frames.append(frame.copy())
                if i % 30 == 0:  # Update reference every 30 frames
                    reference_frame = frame.copy()
            else:
                # Corrupt by using old reference
                corrupted = self._blend_with_reference(frame, reference_frame, 0.3)
                corrupted_frames.append(corrupted)
        
        return corrupted_frames
    
    def _blend_with_reference(self, current: np.ndarray, reference: np.ndarray, blend_factor: float) -> np.ndarray:
        """Blend current frame with reference frame."""
        try:
            return cv2.addWeighted(current, 1 - blend_factor, reference, blend_factor, 0)
        except Exception:
            return current.copy()
    
    def apply_p_frame_duplication(self, frames: list, duplication_rate: float = 0.2) -> list:
        """
        Simulate P-frame duplication for stuttering datamosh effects.
        
        Args:
            frames: List of frames
            duplication_rate: Rate of frame duplication
            
        Returns:
            List of frames with P-frame duplication
        """
        if not frames:
            return frames
        
        duplicated_frames = []
        
        for i, frame in enumerate(frames):
            duplicated_frames.append(frame.copy())
            
            # Randomly duplicate frames
            if random.random() < duplication_rate:
                # Duplicate this frame 1-3 times
                duplicate_count = random.randint(1, 3)
                for _ in range(duplicate_count):
                    duplicated_frames.append(frame.copy())
        
        return duplicated_frames
