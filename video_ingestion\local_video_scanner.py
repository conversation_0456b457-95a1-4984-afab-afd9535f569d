"""
Local video file scanner and metadata extractor.
"""

import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import cv2
from moviepy.editor import VideoFileClip
from config import config

logger = logging.getLogger(__name__)


class VideoMetadata:
    """Container for video metadata."""
    
    def __init__(self, path: Path):
        self.path = path
        self.filename = path.name
        self.size_bytes = path.stat().st_size
        self.duration = 0.0
        self.fps = 0.0
        self.width = 0
        self.height = 0
        self.frame_count = 0
        self.has_audio = False
        self.creation_time = None
        self.format = path.suffix.lower()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage."""
        return {
            'path': str(self.path),
            'filename': self.filename,
            'size_bytes': self.size_bytes,
            'duration': self.duration,
            'fps': self.fps,
            'width': self.width,
            'height': self.height,
            'frame_count': self.frame_count,
            'has_audio': self.has_audio,
            'creation_time': self.creation_time,
            'format': self.format,
        }


class LocalVideoScanner:
    """Scans local directories for video files and extracts metadata."""
    
    def __init__(self):
        self.supported_formats = config.video.supported_formats
    
    def scan_directory(self, directory: Path, recursive: bool = True) -> List[VideoMetadata]:
        """
        Scan directory for video files.
        
        Args:
            directory: Directory to scan
            recursive: Whether to scan subdirectories
            
        Returns:
            List of VideoMetadata objects
        """
        directory = Path(directory)
        if not directory.exists():
            logger.error(f"Directory does not exist: {directory}")
            return []
        
        video_files = []
        
        # Get file pattern
        pattern = "**/*" if recursive else "*"
        
        for file_path in directory.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                try:
                    metadata = self.extract_metadata(file_path)
                    if metadata:
                        video_files.append(metadata)
                        logger.debug(f"Found video: {file_path}")
                except Exception as e:
                    logger.warning(f"Error processing {file_path}: {e}")
        
        logger.info(f"Found {len(video_files)} video files in {directory}")
        return video_files
    
    def extract_metadata(self, video_path: Path) -> Optional[VideoMetadata]:
        """
        Extract metadata from a video file.
        
        Args:
            video_path: Path to video file
            
        Returns:
            VideoMetadata object or None if extraction failed
        """
        try:
            metadata = VideoMetadata(video_path)
            
            # Use OpenCV for basic video properties
            cap = cv2.VideoCapture(str(video_path))
            
            if not cap.isOpened():
                logger.warning(f"Could not open video with OpenCV: {video_path}")
                return None
            
            # Get basic properties
            metadata.width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            metadata.height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            metadata.fps = cap.get(cv2.CAP_PROP_FPS)
            metadata.frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            if metadata.fps > 0:
                metadata.duration = metadata.frame_count / metadata.fps
            
            cap.release()
            
            # Use MoviePy for audio detection and more accurate duration
            try:
                with VideoFileClip(str(video_path)) as clip:
                    metadata.duration = clip.duration
                    metadata.has_audio = clip.audio is not None
            except Exception as e:
                logger.debug(f"MoviePy extraction failed for {video_path}: {e}")
            
            # Validate metadata
            if metadata.duration <= 0 or metadata.width <= 0 or metadata.height <= 0:
                logger.warning(f"Invalid metadata for {video_path}")
                return None
            
            # Check if video is too long
            if metadata.duration > config.video.max_video_duration:
                logger.info(f"Video too long ({metadata.duration:.1f}s), skipping: {video_path}")
                return None
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata from {video_path}: {e}")
            return None
    
    def is_video_file(self, file_path: Path) -> bool:
        """
        Check if a file is a supported video format.
        
        Args:
            file_path: Path to check
            
        Returns:
            True if file is a supported video format
        """
        return file_path.suffix.lower() in self.supported_formats
    
    def get_video_thumbnail(self, video_path: Path, timestamp: float = 1.0) -> Optional[Path]:
        """
        Extract a thumbnail from a video at a specific timestamp.
        
        Args:
            video_path: Path to video file
            timestamp: Time in seconds to extract thumbnail
            
        Returns:
            Path to saved thumbnail image
        """
        try:
            cap = cv2.VideoCapture(str(video_path))
            
            if not cap.isOpened():
                return None
            
            # Set position to timestamp
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            cap.release()
            
            if not ret:
                return None
            
            # Save thumbnail
            thumbnail_dir = config.video.cache_dir / "thumbnails"
            thumbnail_dir.mkdir(parents=True, exist_ok=True)
            
            thumbnail_path = thumbnail_dir / f"{video_path.stem}_thumb.jpg"
            cv2.imwrite(str(thumbnail_path), frame)
            
            return thumbnail_path
            
        except Exception as e:
            logger.error(f"Error extracting thumbnail from {video_path}: {e}")
            return None
