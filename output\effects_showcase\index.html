
<!DOCTYPE html>
<html>
<head>
    <title>Experimental Effects Showcase</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }
        h1 { color: #ff6b6b; text-align: center; }
        .stats { text-align: center; margin: 20px 0; color: #4ecdc4; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .effect { background: #2d2d2d; padding: 15px; border-radius: 8px; text-align: center; }
        .effect img { max-width: 100%; height: auto; border-radius: 4px; }
        .effect h3 { color: #ff6b6b; margin: 10px 0; }
        .original { border: 3px solid #4ecdc4; }
    </style>
</head>
<body>
    <h1>🎬 Experimental Effects Showcase</h1>
    <div class="stats">
        <p>Successfully applied 29/30 effects</p>
        <p>Source: Quick Share videos processed with experimental film pipeline</p>
    </div>

    <div class="grid">
        <div class="effect original">
            <h3>Original Frame</h3>
            <img src="00_original.jpg" alt="Original">
        </div>

        <div class="effect">
            <h3>01 Color Bleed</h3>
            <img src="01_color_bleed.jpg" alt="01 Color Bleed" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>02 Color Shift</h3>
            <img src="02_color_shift.jpg" alt="02 Color Shift" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>03 False Color</h3>
            <img src="03_false_color.jpg" alt="03 False Color" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>04 Chromatic Aberration</h3>
            <img src="04_chromatic_aberration.jpg" alt="04 Chromatic Aberration" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>05 Posterization</h3>
            <img src="05_posterization.jpg" alt="05 Posterization" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>06 Color Inversion</h3>
            <img src="06_color_inversion.jpg" alt="06 Color Inversion" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>07 Wave Distortion</h3>
            <img src="07_wave_distortion.jpg" alt="07 Wave Distortion" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>08 Spiral Distortion</h3>
            <img src="08_spiral_distortion.jpg" alt="08 Spiral Distortion" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>09 Fisheye</h3>
            <img src="09_fisheye.jpg" alt="09 Fisheye" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>10 Kaleidoscope</h3>
            <img src="10_kaleidoscope.jpg" alt="10 Kaleidoscope" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>11 Mirror Horizontal</h3>
            <img src="11_mirror_horizontal.jpg" alt="11 Mirror Horizontal" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>12 Mirror Both</h3>
            <img src="12_mirror_both.jpg" alt="12 Mirror Both" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>13 Pixelation</h3>
            <img src="13_pixelation.jpg" alt="13 Pixelation" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>14 Recursive Zoom</h3>
            <img src="14_recursive_zoom.jpg" alt="14 Recursive Zoom" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>15 Digital Noise</h3>
            <img src="15_digital_noise.jpg" alt="15 Digital Noise" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>16 Scan Lines</h3>
            <img src="16_scan_lines.jpg" alt="16 Scan Lines" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>17 Rgb Shift</h3>
            <img src="17_rgb_shift.jpg" alt="17 Rgb Shift" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>18 Pixel Sorting</h3>
            <img src="18_pixel_sorting.jpg" alt="18 Pixel Sorting" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>19 Block Corruption</h3>
            <img src="19_block_corruption.jpg" alt="19 Block Corruption" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>20 Vhs Artifacts</h3>
            <img src="20_vhs_artifacts.jpg" alt="20 Vhs Artifacts" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>21 Bit Crush</h3>
            <img src="21_bit_crush.jpg" alt="21 Bit Crush" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>22 Compression Artifacts</h3>
            <img src="22_compression_artifacts.jpg" alt="22 Compression Artifacts" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>23 Recursive Overlay</h3>
            <img src="23_recursive_overlay.jpg" alt="23 Recursive Overlay" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>24 Fractal Feedback</h3>
            <img src="24_fractal_feedback.jpg" alt="24 Fractal Feedback" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>25 Recursive Rotation</h3>
            <img src="25_recursive_rotation.jpg" alt="25 Recursive Rotation" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>26 Psychedelic</h3>
            <img src="26_psychedelic.jpg" alt="26 Psychedelic" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>27 Digital Decay</h3>
            <img src="27_digital_decay.jpg" alt="27 Digital Decay" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>28 Warped Colors</h3>
            <img src="28_warped_colors.jpg" alt="28 Warped Colors" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>29 Glitch Spiral</h3>
            <img src="29_glitch_spiral.jpg" alt="29 Glitch Spiral" onerror="this.style.display='none'">
        </div>

        <div class="effect">
            <h3>30 Recursive Glitch</h3>
            <img src="30_recursive_glitch.jpg" alt="30 Recursive Glitch" onerror="this.style.display='none'">
        </div>

    </div>
</body>
</html>
