"""
Simple test of Gemini API with function calling.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_gemini_basic():
    """Test basic Gemini API functionality."""
    
    print("Testing Gemini API...")
    
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ GEMINI_API_KEY not found in environment")
        return False
    
    print(f"✅ API Key found: {api_key[:10]}...")
    
    try:
        from google import genai
        from google.genai import types
        
        print("✅ Imported genai successfully")
        
        # Test basic text generation
        client = genai.Client(api_key=api_key)
        print("✅ Created client successfully")
        
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents="Hello, can you respond with a simple greeting?"
        )
        
        print("✅ Basic generation successful")
        print(f"Response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_calling():
    """Test function calling with Gemini."""
    
    print("\nTesting function calling...")
    
    try:
        from google import genai
        from google.genai import types
        
        # Define a simple function
        effect_function = {
            "name": "apply_effect",
            "description": "Apply a visual effect to video",
            "parameters": {
                "type": "object",
                "properties": {
                    "effect_type": {
                        "type": "string",
                        "description": "Type of effect to apply",
                        "enum": ["glitch", "color", "distortion"]
                    },
                    "intensity": {
                        "type": "number",
                        "description": "Effect intensity from 0.0 to 1.0",
                        "minimum": 0.0,
                        "maximum": 1.0
                    }
                },
                "required": ["effect_type", "intensity"]
            }
        }
        
        # Configure client with tools
        client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        tools = types.Tool(function_declarations=[effect_function])
        config = types.GenerateContentConfig(tools=[tools])
        
        # Send request
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents="Apply a glitch effect with medium intensity to create a cyberpunk aesthetic.",
            config=config
        )
        
        print("✅ Function calling request successful")
        
        # Check for function calls
        function_called = False
        if (response.candidates and 
            response.candidates[0].content.parts):
            
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'function_call') and part.function_call:
                    function_call = part.function_call
                    print(f"✅ Function called: {function_call.name}")
                    print(f"   Arguments: {dict(function_call.args)}")
                    function_called = True
        
        if not function_called:
            print("⚠️ No function call detected")
            if hasattr(response, 'text'):
                print(f"Text response: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Function calling error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Simple Gemini API Test")
    print("=" * 40)
    
    # Test 1: Basic API
    if test_gemini_basic():
        print("✅ Basic API test passed")
    else:
        print("❌ Basic API test failed")
        exit(1)
    
    # Test 2: Function calling
    if test_function_calling():
        print("✅ Function calling test passed")
    else:
        print("❌ Function calling test failed")
    
    print("\n🎉 All tests completed!")
