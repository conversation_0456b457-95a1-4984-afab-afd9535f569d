# LLM-Driven Experimental Film Pipeline

An AI-powered video processing pipeline that uses Large Language Models to understand, search, and creatively manipulate video content for experimental filmmaking.

## Features

- **Intelligent Video Ingestion**: Download from YouTube or scan local directories
- **LLM-Based Content Analysis**: Uses GPT-4 Vision or Gemini to understand video content
- **Semantic Search**: Natural language search for video segments using LLM function calling
- **Creative Film Planning**: AI-generated experimental film concepts and structures
- **Experimental Effects**: Datamoshing, color bleeding, frame warping, and more
- **Automated Composition**: Combines segments with effects into final experimental films

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd experimental-film-pipeline
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
# Create .env file
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
```

## Quick Start

### 1. Download and Index Videos

```bash
# Download a YouTube video
python main.py download --url "https://www.youtube.com/watch?v=VIDEO_ID"

# Or scan local directory
python main.py index --input-dir ./my_videos
```

### 2. Search for Content

```bash
# Search using natural language
python main.py search "urban scenes with people walking"
python main.py search "abstract colorful patterns"
python main.py search "dramatic lighting and shadows"
```

### 3. Create Experimental Films

```bash
# Generate an experimental film
python main.py create "digital anxiety" --duration 90 --style "glitch"
python main.py create "urban isolation" --duration 60 --style "surreal"
```

### 4. Web Interface (Optional)

```bash
# Start web interface for interactive exploration
python main.py web
```

## Architecture

### Core Components

- **Video Ingestion** (`video_ingestion/`): YouTube downloading and local file scanning
- **Video Analysis** (`video_analysis/`): LLM-based content understanding and indexing
- **Semantic Search** (`search/`): Function-calling interface for content discovery
- **Creative Planning** (`creative/`): AI-driven film concept generation
- **Effects Engine** (`effects/`): Experimental video manipulation tools
- **Video Editing** (`editing/`): Composition and rendering pipeline
- **Storage** (`storage/`): SQLite database for metadata and search indices

### LLM Integration

The pipeline supports both OpenAI GPT-4 Vision and Google Gemini:

- **Content Analysis**: Describes visual content, style, and emotional qualities
- **Function Calling**: Semantic search with structured queries
- **Creative Planning**: Generates experimental film concepts and structures

### Experimental Effects

- **Datamoshing**: Motion vector corruption and I-frame manipulation
- **Color Effects**: Channel bleeding, chromatic aberration, false color
- **Frame Warping**: Recursive distortions and geometric transformations
- **Glitch Effects**: Digital artifacts and compression errors

## Configuration

Edit `config.py` to customize:

- LLM providers and models
- Video processing settings
- Effect parameters
- Database configuration

## Examples

### Programmatic Usage

```python
from video_ingestion.youtube_downloader import YouTubeDownloader
from video_analysis.content_indexer import ContentIndexer
from search.semantic_search import SemanticSearch
from creative.film_planner import FilmPlanner
from editing.video_compositor import VideoCompositor

# Download and index a video
downloader = YouTubeDownloader()
video_path = downloader.download_video("https://www.youtube.com/watch?v=VIDEO_ID")

indexer = ContentIndexer()
indexer.index_video(video_path)

# Search for content
search = SemanticSearch()
results = search.search("dreamy landscape shots")

# Create experimental film
planner = FilmPlanner()
film_plan = planner.create_plan("digital nostalgia", duration=120)

compositor = VideoCompositor()
for segment in film_plan.segments:
    # Find matching content and add to composition
    matches = search.search(segment.search_query, limit=1)
    if matches:
        compositor.add_segment(
            video_path=matches[0].video_path,
            start_time=matches[0].start_time,
            end_time=matches[0].end_time,
            effects=segment.effects,
            target_duration=segment.duration
        )

# Render final film
compositor.render("output/digital_nostalgia.mp4")
```

### Effect Examples

```python
from effects.datamosh import DatamoshEffect
from effects.color_effects import ColorEffects

# Apply datamoshing
datamosh = DatamoshEffect()
datamoshed_frames = datamosh.apply_datamosh(frames, intensity=0.7)

# Apply color bleeding
color_fx = ColorEffects()
bleeding_frame = color_fx.apply_color_bleed(frame, intensity=0.5)
```

## API Reference

### Main CLI Commands

- `download`: Download videos from YouTube
- `index`: Index videos for semantic search
- `search`: Search for video segments
- `create`: Generate experimental films
- `web`: Start web interface

### Core Classes

- `ContentIndexer`: Analyzes and indexes video content
- `SemanticSearch`: LLM-powered content search
- `FilmPlanner`: AI-driven creative planning
- `VideoCompositor`: Video composition and rendering
- `DatamoshEffect`: Datamoshing effects
- `ColorEffects`: Color manipulation effects

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

- OpenAI for GPT-4 Vision API
- Google for Gemini API
- OpenCV and MoviePy for video processing
- yt-dlp for YouTube downloading
