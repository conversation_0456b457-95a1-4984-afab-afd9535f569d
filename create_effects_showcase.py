"""
Create a showcase of all experimental effects.
"""

import cv2
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_effects_showcase():
    """Create a comprehensive showcase of all effects."""

    logger.info("Creating comprehensive effects showcase...")

    try:
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        from effects.recursive_effects import RecursiveEffects
        from video_analysis.frame_extractor import FrameExtractor

        # Get a source video
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))

        if not video_files:
            logger.error("No video files found")
            return False

        source_video = video_files[0]
        logger.info(f"Using source video: {source_video.name}")

        # Extract a single frame for testing
        extractor = FrameExtractor()

        # Try to get a frame from the middle of the video
        cap = cv2.VideoCapture(str(source_video))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)

        # Get frame from 2 seconds in
        frame_number = int(2 * fps)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, test_frame = cap.read()
        cap.release()

        if not ret:
            logger.error("Could not extract test frame")
            return False

        logger.info(f"Extracted test frame: {test_frame.shape}")

        # Initialize effects
        color_fx = ColorEffects()
        warp_fx = FrameWarpingEffect()
        glitch_fx = GlitchEffects()
        datamosh_fx = DatamoshEffect()
        recursive_fx = RecursiveEffects()

        # Create output directory
        output_dir = Path("./output/effects_showcase")
        output_dir.mkdir(parents=True, exist_ok=True)

        # Save original
        cv2.imwrite(str(output_dir / "00_original.jpg"), test_frame)
        logger.info("Saved original frame")

        # Test all effects
        effects_list = [
            # Color Effects
            ("01_color_bleed", lambda f: color_fx.apply_color_bleed(f, intensity=0.5)),
            ("02_color_shift", lambda f: color_fx.apply_color_shift(f)),
            ("03_false_color", lambda f: color_fx.apply_false_color(f, color_map="thermal")),
            ("04_chromatic_aberration", lambda f: color_fx.apply_chromatic_aberration(f, intensity=0.8)),
            ("05_posterization", lambda f: color_fx.apply_posterization(f, levels=4)),
            ("06_color_inversion", lambda f: color_fx.apply_color_inversion(f, channels="rg")),

            # Frame Warping Effects
            ("07_wave_distortion", lambda f: warp_fx.apply_wave_distortion(f, amplitude=30, frequency=0.05)),
            ("08_spiral_distortion", lambda f: warp_fx.apply_spiral_distortion(f, intensity=0.6)),
            ("09_fisheye", lambda f: warp_fx.apply_fisheye_distortion(f, strength=0.8)),
            ("10_kaleidoscope", lambda f: warp_fx.apply_kaleidoscope(f, segments=8)),
            ("11_mirror_horizontal", lambda f: warp_fx.apply_mirror_effect(f, direction="horizontal")),
            ("12_mirror_both", lambda f: warp_fx.apply_mirror_effect(f, direction="both")),
            ("13_pixelation", lambda f: warp_fx.apply_pixelation(f, pixel_size=12)),
            ("14_recursive_zoom", lambda f: warp_fx.apply_recursive_zoom(f, zoom_factor=1.2, iterations=4)),

            # Glitch Effects
            ("15_digital_noise", lambda f: glitch_fx.apply_digital_noise(f, intensity=0.3)),
            ("16_scan_lines", lambda f: glitch_fx.apply_scan_lines(f, line_spacing=4, intensity=0.7)),
            ("17_rgb_shift", lambda f: glitch_fx.apply_rgb_shift(f, shift_amount=8)),
            ("18_pixel_sorting", lambda f: glitch_fx.apply_pixel_sorting(f, direction="horizontal", threshold=120)),
            ("19_block_corruption", lambda f: glitch_fx.apply_block_corruption(f, block_size=20, corruption_rate=0.15)),
            ("20_vhs_artifacts", lambda f: glitch_fx.apply_vhs_artifacts(f)),
            ("21_bit_crush", lambda f: glitch_fx.apply_bit_crush(f, bits=3)),
            ("22_compression_artifacts", lambda f: glitch_fx.apply_compression_artifacts(f, quality=5)),

            # Recursive Effects
            ("23_recursive_overlay", lambda f: recursive_fx.apply_recursive_overlay(f, scale_factor=0.7, offset_x=30, offset_y=30)),
            ("24_fractal_feedback", lambda f: recursive_fx.apply_fractal_feedback(f, iterations=4)),
            ("25_recursive_rotation", lambda f: recursive_fx.apply_recursive_rotation(f, rotation_angle=20, scale_factor=0.85)),

            # Combined Effects
            ("26_psychedelic", lambda f: warp_fx.apply_kaleidoscope(color_fx.apply_color_shift(f), segments=6)),
            ("27_digital_decay", lambda f: glitch_fx.apply_digital_noise(glitch_fx.apply_scan_lines(f), intensity=0.4)),
            ("28_warped_colors", lambda f: color_fx.apply_color_bleed(warp_fx.apply_wave_distortion(f))),
            ("29_glitch_spiral", lambda f: glitch_fx.apply_rgb_shift(warp_fx.apply_spiral_distortion(f))),
            ("30_recursive_glitch", lambda f: glitch_fx.apply_block_corruption(recursive_fx.apply_recursive_overlay(f))),
        ]

        logger.info(f"Applying {len(effects_list)} effects...")

        successful_effects = 0
        for i, (name, effect_func) in enumerate(effects_list):
            try:
                logger.info(f"[{i+1}/{len(effects_list)}] Applying {name}...")
                result_frame = effect_func(test_frame.copy())

                # Ensure the result is valid
                if result_frame is not None and result_frame.shape == test_frame.shape:
                    cv2.imwrite(str(output_dir / f"{name}.jpg"), result_frame)
                    successful_effects += 1
                else:
                    logger.warning(f"Invalid result for {name}")

            except Exception as e:
                logger.error(f"Error applying {name}: {e}")

        logger.info(f"✅ Effects showcase created in {output_dir}")
        logger.info(f"Successfully applied {successful_effects}/{len(effects_list)} effects")

        # Create an index HTML file
        create_html_index(output_dir, effects_list, successful_effects)

        return True

    except Exception as e:
        logger.error(f"Error creating effects showcase: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_html_index(output_dir: Path, effects_list: list, successful_effects: int):
    """Create an HTML index to view all effects."""

    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Experimental Effects Showcase</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #fff; }}
        h1 {{ color: #ff6b6b; text-align: center; }}
        .stats {{ text-align: center; margin: 20px 0; color: #4ecdc4; }}
        .grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
        .effect {{ background: #2d2d2d; padding: 15px; border-radius: 8px; text-align: center; }}
        .effect img {{ max-width: 100%; height: auto; border-radius: 4px; }}
        .effect h3 {{ color: #ff6b6b; margin: 10px 0; }}
        .original {{ border: 3px solid #4ecdc4; }}
    </style>
</head>
<body>
    <h1>🎬 Experimental Effects Showcase</h1>
    <div class="stats">
        <p>Successfully applied {successful_effects}/{len(effects_list)} effects</p>
        <p>Source: Quick Share videos processed with experimental film pipeline</p>
    </div>

    <div class="grid">
        <div class="effect original">
            <h3>Original Frame</h3>
            <img src="00_original.jpg" alt="Original">
        </div>
"""

    for name, _ in effects_list:
        effect_name = name.replace("_", " ").title()
        html_content += f"""
        <div class="effect">
            <h3>{effect_name}</h3>
            <img src="{name}.jpg" alt="{effect_name}" onerror="this.style.display='none'">
        </div>
"""

    html_content += """
    </div>
</body>
</html>
"""

    with open(output_dir / "index.html", "w", encoding="utf-8") as f:
        f.write(html_content)

    logger.info(f"Created HTML index: {output_dir / 'index.html'}")

if __name__ == "__main__":
    logger.info("🎨 Starting Effects Showcase Creation")

    if create_effects_showcase():
        logger.info("✅ Effects showcase completed successfully!")
        logger.info("Open output/effects_showcase/index.html to view all effects")
    else:
        logger.error("❌ Effects showcase failed")
