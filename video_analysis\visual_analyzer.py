"""
LLM-based visual content analysis for video frames.
"""

import logging
import base64
from pathlib import Path
from typing import List, Dict, Any, Optional
import cv2
import numpy as np
from openai import OpenAI
from google import genai
from google.genai import types
import json

from config import config

logger = logging.getLogger(__name__)


class VisualAnalyzer:
    """Analyzes video frames using LLM vision capabilities."""
    
    def __init__(self, provider: str = None):
        self.provider = provider or config.llm.default_provider
        
        if self.provider == "openai":
            self.openai_client = OpenAI(api_key=config.llm.openai_api_key)
        elif self.provider == "gemini":
            self.gemini_client = genai.Client(api_key=config.llm.gemini_api_key)
        else:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")
    
    def analyze_frame(self, frame: np.ndarray, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Analyze a single frame using LLM vision.
        
        Args:
            frame: Frame as numpy array
            analysis_type: Type of analysis ("comprehensive", "style", "content", "emotion")
            
        Returns:
            Analysis results dictionary
        """
        # Convert frame to base64
        frame_b64 = self._frame_to_base64(frame)
        
        if self.provider == "openai":
            return self._analyze_with_openai(frame_b64, analysis_type)
        elif self.provider == "gemini":
            return self._analyze_with_gemini(frame_b64, analysis_type)
    
    def analyze_sequence(self, frames: List[np.ndarray], context: str = "") -> Dict[str, Any]:
        """
        Analyze a sequence of frames for temporal patterns.
        
        Args:
            frames: List of frames as numpy arrays
            context: Additional context about the sequence
            
        Returns:
            Sequence analysis results
        """
        # For now, analyze key frames and summarize
        # In the future, could use video-specific models
        
        frame_analyses = []
        for i, frame in enumerate(frames):
            if i % 3 == 0:  # Analyze every 3rd frame to reduce API calls
                analysis = self.analyze_frame(frame, "content")
                frame_analyses.append(analysis)
        
        # Summarize sequence
        return self._summarize_sequence(frame_analyses, context)
    
    def _analyze_with_openai(self, frame_b64: str, analysis_type: str) -> Dict[str, Any]:
        """Analyze frame using OpenAI GPT-4 Vision."""
        
        prompts = {
            "comprehensive": """Analyze this video frame comprehensively. Describe:
1. Visual content and objects
2. Scene setting and environment
3. People and their actions/emotions
4. Visual style and cinematography
5. Colors, lighting, and mood
6. Any text or graphics visible
7. Artistic or experimental elements

Provide a detailed but concise analysis.""",
            
            "style": """Analyze the visual style of this frame. Focus on:
1. Cinematographic techniques (camera angle, composition, framing)
2. Color palette and lighting
3. Visual effects or filters
4. Artistic style (realistic, stylized, experimental)
5. Mood and atmosphere
6. Any experimental or avant-garde elements

Describe the style in detail.""",
            
            "content": """Describe the content of this frame focusing on:
1. Main subjects and objects
2. Actions taking place
3. Setting and location
4. Relationships between elements
5. Any narrative elements

Be specific and descriptive.""",
            
            "emotion": """Analyze the emotional content of this frame:
1. Emotions expressed by people
2. Overall mood and atmosphere
3. Emotional impact of colors and lighting
4. Tension, energy, or calmness
5. Psychological elements

Describe the emotional qualities."""
        }
        
        try:
            response = self.openai_client.chat.completions.create(
                model=config.llm.openai_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompts.get(analysis_type, prompts["comprehensive"])},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{frame_b64}"}
                            }
                        ]
                    }
                ],
                max_tokens=config.llm.max_tokens,
                temperature=config.llm.temperature
            )
            
            analysis_text = response.choices[0].message.content
            
            return {
                "provider": "openai",
                "analysis_type": analysis_type,
                "description": analysis_text,
                "tokens_used": response.usage.total_tokens if response.usage else 0,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"OpenAI analysis failed: {e}")
            return {
                "provider": "openai",
                "analysis_type": analysis_type,
                "description": f"Analysis failed: {str(e)}",
                "tokens_used": 0,
                "success": False
            }
    
    def _analyze_with_gemini(self, frame_b64: str, analysis_type: str) -> Dict[str, Any]:
        """Analyze frame using Google Gemini."""
        
        prompts = {
            "comprehensive": "Analyze this video frame comprehensively, describing visual content, style, mood, and any experimental elements.",
            "style": "Analyze the visual and cinematographic style of this frame, focusing on artistic and experimental elements.",
            "content": "Describe the main content and subjects in this frame, focusing on what is happening.",
            "emotion": "Analyze the emotional content and mood of this frame."
        }
        
        try:
            # Convert base64 to bytes for Gemini
            image_data = base64.b64decode(frame_b64)
            
            response = self.gemini_client.models.generate_content(
                model=config.llm.gemini_model,
                contents=[
                    prompts.get(analysis_type, prompts["comprehensive"]),
                    {"mime_type": "image/jpeg", "data": image_data}
                ]
            )
            
            analysis_text = response.text
            
            return {
                "provider": "gemini",
                "analysis_type": analysis_type,
                "description": analysis_text,
                "tokens_used": 0,  # Gemini doesn't provide token count in this API
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Gemini analysis failed: {e}")
            return {
                "provider": "gemini",
                "analysis_type": analysis_type,
                "description": f"Analysis failed: {str(e)}",
                "tokens_used": 0,
                "success": False
            }
    
    def _summarize_sequence(self, frame_analyses: List[Dict[str, Any]], context: str) -> Dict[str, Any]:
        """Summarize a sequence of frame analyses."""
        
        if not frame_analyses:
            return {"summary": "No frames analyzed", "success": False}
        
        # Combine descriptions
        descriptions = [analysis.get("description", "") for analysis in frame_analyses if analysis.get("success")]
        
        if not descriptions:
            return {"summary": "All frame analyses failed", "success": False}
        
        # Create summary prompt
        combined_text = "\n\n".join([f"Frame {i+1}: {desc}" for i, desc in enumerate(descriptions)])
        
        summary_prompt = f"""Analyze this sequence of video frames and provide a summary focusing on:
1. Overall narrative or action progression
2. Visual style consistency or changes
3. Mood and atmosphere evolution
4. Key visual elements and themes
5. Experimental or artistic qualities

Context: {context}

Frame analyses:
{combined_text}

Provide a concise summary of the sequence."""
        
        try:
            if self.provider == "openai":
                response = self.openai_client.chat.completions.create(
                    model="gpt-4",  # Use text model for summary
                    messages=[{"role": "user", "content": summary_prompt}],
                    max_tokens=config.llm.max_tokens,
                    temperature=config.llm.temperature
                )
                summary = response.choices[0].message.content
                
            elif self.provider == "gemini":
                response = self.gemini_client.models.generate_content(
                    model=config.llm.gemini_model,
                    contents=[summary_prompt]
                )
                summary = response.text
            
            return {
                "summary": summary,
                "frame_count": len(frame_analyses),
                "successful_analyses": len(descriptions),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Sequence summary failed: {e}")
            return {
                "summary": f"Summary failed: {str(e)}",
                "frame_count": len(frame_analyses),
                "successful_analyses": len(descriptions),
                "success": False
            }
    
    def _frame_to_base64(self, frame: np.ndarray) -> str:
        """Convert frame to base64 string."""
        # Encode frame as JPEG
        _, buffer = cv2.imencode('.jpg', frame)
        # Convert to base64
        frame_b64 = base64.b64encode(buffer).decode('utf-8')
        return frame_b64
