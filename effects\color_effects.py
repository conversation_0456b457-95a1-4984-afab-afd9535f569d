"""
Color manipulation and bleeding effects for experimental video.
"""

import logging
import numpy as np
import cv2
from typing import Tuple, Optional
import random

try:
    from config import config
except ImportError:
    # Fallback config
    class Config:
        class effects:
            color_bleed_intensity = 0.3
            color_shift_range = (-50, 50)
    config = Config()

logger = logging.getLogger(__name__)


class ColorEffects:
    """Implements various color manipulation effects."""

    def __init__(self):
        self.bleed_intensity = config.effects.color_bleed_intensity
        self.shift_range = config.effects.color_shift_range

    def apply_color_bleed(self, frame: np.ndarray, intensity: float = None) -> np.ndarray:
        """
        Apply color bleeding effect by separating and shifting color channels.

        Args:
            frame: Input frame as numpy array
            intensity: Effect intensity (0.0 to 1.0)

        Returns:
            Frame with color bleeding effect
        """
        intensity = intensity or self.bleed_intensity

        if len(frame.shape) != 3:
            return frame

        height, width, channels = frame.shape
        result = frame.copy()

        # Separate color channels
        b, g, r = cv2.split(frame)

        # Calculate shift amounts
        max_shift = int(intensity * 20)  # Maximum 20 pixel shift

        # Apply different shifts to each channel
        r_shift_x = random.randint(-max_shift, max_shift)
        r_shift_y = random.randint(-max_shift//2, max_shift//2)

        g_shift_x = random.randint(-max_shift//2, max_shift//2)
        g_shift_y = random.randint(-max_shift, max_shift)

        b_shift_x = random.randint(-max_shift//3, max_shift//3)
        b_shift_y = random.randint(-max_shift//3, max_shift//3)

        # Shift channels
        r_shifted = self._shift_channel(r, r_shift_x, r_shift_y)
        g_shifted = self._shift_channel(g, g_shift_x, g_shift_y)
        b_shifted = self._shift_channel(b, b_shift_x, b_shift_y)

        # Recombine channels
        result = cv2.merge([b_shifted, g_shifted, r_shifted])

        return result

    def _shift_channel(self, channel: np.ndarray, shift_x: int, shift_y: int) -> np.ndarray:
        """Shift a single color channel."""
        height, width = channel.shape

        # Create transformation matrix
        M = np.float32([[1, 0, shift_x], [0, 1, shift_y]])

        # Apply transformation
        shifted = cv2.warpAffine(channel, M, (width, height), borderMode=cv2.BORDER_WRAP)

        return shifted

    def apply_chromatic_aberration(self, frame: np.ndarray, intensity: float = 0.5) -> np.ndarray:
        """
        Apply chromatic aberration effect.

        Args:
            frame: Input frame
            intensity: Effect intensity

        Returns:
            Frame with chromatic aberration
        """
        if len(frame.shape) != 3:
            return frame

        height, width = frame.shape[:2]
        center_x, center_y = width // 2, height // 2

        # Separate channels
        b, g, r = cv2.split(frame)

        # Create radial distortion for each channel
        r_distorted = self._apply_radial_distortion(r, center_x, center_y, intensity * 1.2)
        g_distorted = self._apply_radial_distortion(g, center_x, center_y, intensity)
        b_distorted = self._apply_radial_distortion(b, center_x, center_y, intensity * 0.8)

        # Recombine
        result = cv2.merge([b_distorted, g_distorted, r_distorted])

        return result

    def _apply_radial_distortion(self, channel: np.ndarray, center_x: int, center_y: int, intensity: float) -> np.ndarray:
        """Apply radial distortion to a channel."""
        height, width = channel.shape

        # Create coordinate grids
        y, x = np.ogrid[:height, :width]

        # Calculate distance from center
        dx = x - center_x
        dy = y - center_y
        distance = np.sqrt(dx*dx + dy*dy)

        # Normalize distance
        max_distance = np.sqrt(center_x*center_x + center_y*center_y)
        normalized_distance = distance / max_distance

        # Apply distortion
        distortion_factor = 1 + intensity * normalized_distance * 0.1

        # Calculate new coordinates
        new_x = center_x + dx / distortion_factor
        new_y = center_y + dy / distortion_factor

        # Clamp coordinates
        new_x = np.clip(new_x, 0, width - 1)
        new_y = np.clip(new_y, 0, height - 1)

        # Interpolate
        distorted = cv2.remap(channel, new_x.astype(np.float32), new_y.astype(np.float32), cv2.INTER_LINEAR)

        return distorted

    def apply_color_shift(self, frame: np.ndarray, shift_amount: Optional[Tuple[int, int, int]] = None) -> np.ndarray:
        """
        Apply color channel shifting.

        Args:
            frame: Input frame
            shift_amount: RGB shift amounts, or None for random

        Returns:
            Color-shifted frame
        """
        if len(frame.shape) != 3:
            return frame

        if shift_amount is None:
            # Random shift
            shift_amount = (
                random.randint(*self.shift_range),
                random.randint(*self.shift_range),
                random.randint(*self.shift_range)
            )

        # Apply shifts to each channel
        result = frame.copy().astype(np.int16)  # Use int16 to prevent overflow

        result[:, :, 0] += shift_amount[0]  # Blue
        result[:, :, 1] += shift_amount[1]  # Green
        result[:, :, 2] += shift_amount[2]  # Red

        # Clamp values
        result = np.clip(result, 0, 255).astype(np.uint8)

        return result

    def apply_false_color(self, frame: np.ndarray, color_map: str = "random") -> np.ndarray:
        """
        Apply false color mapping.

        Args:
            frame: Input frame
            color_map: Color map to use ("random", "thermal", "rainbow", etc.)

        Returns:
            False-colored frame
        """
        # Convert to grayscale first
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Apply color map
        if color_map == "random":
            color_maps = [cv2.COLORMAP_JET, cv2.COLORMAP_HSV, cv2.COLORMAP_PLASMA,
                         cv2.COLORMAP_VIRIDIS, cv2.COLORMAP_INFERNO]
            color_map_cv = random.choice(color_maps)
        elif color_map == "thermal":
            color_map_cv = cv2.COLORMAP_JET
        elif color_map == "rainbow":
            color_map_cv = cv2.COLORMAP_HSV
        else:
            color_map_cv = cv2.COLORMAP_JET

        false_colored = cv2.applyColorMap(gray, color_map_cv)

        return false_colored

    def apply_color_inversion(self, frame: np.ndarray, channels: str = "all") -> np.ndarray:
        """
        Apply color inversion to specific channels.

        Args:
            frame: Input frame
            channels: Which channels to invert ("all", "r", "g", "b", "rg", etc.)

        Returns:
            Color-inverted frame
        """
        result = frame.copy()

        if "r" in channels or channels == "all":
            result[:, :, 2] = 255 - result[:, :, 2]

        if "g" in channels or channels == "all":
            result[:, :, 1] = 255 - result[:, :, 1]

        if "b" in channels or channels == "all":
            result[:, :, 0] = 255 - result[:, :, 0]

        return result

    def apply_posterization(self, frame: np.ndarray, levels: int = 4) -> np.ndarray:
        """
        Apply posterization effect to reduce color levels.

        Args:
            frame: Input frame
            levels: Number of color levels per channel

        Returns:
            Posterized frame
        """
        # Calculate step size
        step = 256 // levels

        # Quantize each channel
        result = frame.copy()
        result = (result // step) * step

        return result

    def apply_channel_mixing(self, frame: np.ndarray, mix_matrix: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Apply channel mixing using a transformation matrix.

        Args:
            frame: Input frame
            mix_matrix: 3x3 mixing matrix, or None for random

        Returns:
            Channel-mixed frame
        """
        if len(frame.shape) != 3:
            return frame

        if mix_matrix is None:
            # Create random mixing matrix
            mix_matrix = np.random.rand(3, 3) * 2 - 1  # Values between -1 and 1
            # Normalize to prevent extreme brightness changes
            mix_matrix = mix_matrix / np.sum(np.abs(mix_matrix), axis=1, keepdims=True)

        # Reshape frame for matrix multiplication
        height, width, channels = frame.shape
        reshaped = frame.reshape(-1, 3).astype(np.float32)

        # Apply mixing matrix
        mixed = np.dot(reshaped, mix_matrix.T)

        # Clamp and reshape back
        mixed = np.clip(mixed, 0, 255).astype(np.uint8)
        result = mixed.reshape(height, width, channels)

        return result
