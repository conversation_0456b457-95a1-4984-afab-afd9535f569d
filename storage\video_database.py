"""
Database management for video metadata and analysis results.
"""

import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import sqlite3
from contextlib import contextmanager
import json

from video_ingestion.local_video_scanner import VideoMetadata
from config import config

logger = logging.getLogger(__name__)


class VideoDatabase:
    """SQLite database for storing video metadata and analysis results."""
    
    def __init__(self, db_path: Optional[Path] = None):
        self.db_path = db_path or Path("film_pipeline.db")
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables."""
        with self._get_connection() as conn:
            # Videos table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS videos (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    path TEXT UNIQUE NOT NULL,
                    filename TEXT NOT NULL,
                    size_bytes INTEGER,
                    duration REAL,
                    fps REAL,
                    width INTEGER,
                    height INTEGER,
                    frame_count INTEGER,
                    has_audio BOOLEAN,
                    format TEXT,
                    content_hash TEXT,
                    indexed BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Video segments table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS video_segments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    video_id INTEGER,
                    start_time REAL NOT NULL,
                    end_time REAL NOT NULL,
                    duration REAL NOT NULL,
                    description TEXT,
                    style_analysis TEXT,
                    content_analysis TEXT,
                    emotion_analysis TEXT,
                    keyframes_count INTEGER,
                    content_hash TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (video_id) REFERENCES videos (id)
                )
            """)
            
            # Search index table for full-text search
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS segments_fts USING fts5(
                    segment_id,
                    description,
                    style_analysis,
                    content_analysis,
                    emotion_analysis,
                    content='video_segments',
                    content_rowid='id'
                )
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_videos_path ON videos(path)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_videos_indexed ON videos(indexed)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_segments_video_id ON video_segments(video_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_segments_time ON video_segments(start_time, end_time)")
            
            conn.commit()
    
    @contextmanager
    def _get_connection(self):
        """Get database connection with proper cleanup."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def store_video_metadata(self, metadata: VideoMetadata, content_hash: str) -> int:
        """
        Store video metadata in database.
        
        Args:
            metadata: VideoMetadata object
            content_hash: Content hash for change detection
            
        Returns:
            Video ID
        """
        with self._get_connection() as conn:
            cursor = conn.execute("""
                INSERT OR REPLACE INTO videos 
                (path, filename, size_bytes, duration, fps, width, height, 
                 frame_count, has_audio, format, content_hash, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                str(metadata.path), metadata.filename, metadata.size_bytes,
                metadata.duration, metadata.fps, metadata.width, metadata.height,
                metadata.frame_count, metadata.has_audio, metadata.format,
                content_hash, datetime.now()
            ))
            
            video_id = cursor.lastrowid
            conn.commit()
            return video_id
    
    def store_video_segment(self, segment) -> int:
        """
        Store video segment analysis.
        
        Args:
            segment: VideoSegment object
            
        Returns:
            Segment ID
        """
        # Get video ID
        video_id = self._get_video_id(Path(segment.video_path))
        
        if not video_id:
            raise ValueError(f"Video not found in database: {segment.video_path}")
        
        with self._get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO video_segments 
                (video_id, start_time, end_time, duration, description, 
                 style_analysis, content_analysis, emotion_analysis, 
                 keyframes_count, content_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                video_id, segment.start_time, segment.end_time, segment.duration,
                segment.description, segment.style_analysis, segment.content_analysis,
                segment.emotion_analysis, segment.keyframes_count, segment.content_hash
            ))
            
            segment_id = cursor.lastrowid
            
            # Update FTS index
            conn.execute("""
                INSERT INTO segments_fts 
                (segment_id, description, style_analysis, content_analysis, emotion_analysis)
                VALUES (?, ?, ?, ?, ?)
            """, (
                segment_id, segment.description, segment.style_analysis,
                segment.content_analysis, segment.emotion_analysis
            ))
            
            conn.commit()
            return segment_id
    
    def is_video_indexed(self, video_path: Path) -> bool:
        """Check if video is already indexed."""
        with self._get_connection() as conn:
            cursor = conn.execute(
                "SELECT indexed FROM videos WHERE path = ?",
                (str(video_path),)
            )
            result = cursor.fetchone()
            return result and result['indexed']
    
    def mark_video_indexed(self, video_path: Path, content_hash: str):
        """Mark video as indexed."""
        with self._get_connection() as conn:
            conn.execute("""
                UPDATE videos 
                SET indexed = TRUE, content_hash = ?, updated_at = ?
                WHERE path = ?
            """, (content_hash, datetime.now(), str(video_path)))
            conn.commit()
    
    def _get_video_id(self, video_path: Path) -> Optional[int]:
        """Get video ID by path."""
        with self._get_connection() as conn:
            cursor = conn.execute(
                "SELECT id FROM videos WHERE path = ?",
                (str(video_path),)
            )
            result = cursor.fetchone()
            return result['id'] if result else None
    
    def get_video_segments(self, video_path: Path) -> List[Dict[str, Any]]:
        """Get all segments for a video."""
        video_id = self._get_video_id(video_path)
        
        if not video_id:
            return []
        
        with self._get_connection() as conn:
            cursor = conn.execute("""
                SELECT s.*, v.path as video_path
                FROM video_segments s
                JOIN videos v ON s.video_id = v.id
                WHERE s.video_id = ?
                ORDER BY s.start_time
            """, (video_id,))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def search_segments(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search segments using full-text search.
        
        Args:
            query: Search query
            limit: Maximum number of results
            
        Returns:
            List of matching segments with similarity scores
        """
        with self._get_connection() as conn:
            cursor = conn.execute("""
                SELECT s.*, v.path as video_path, fts.rank
                FROM segments_fts fts
                JOIN video_segments s ON fts.segment_id = s.id
                JOIN videos v ON s.video_id = v.id
                WHERE segments_fts MATCH ?
                ORDER BY fts.rank
                LIMIT ?
            """, (query, limit))
            
            return [dict(row) for row in cursor.fetchall()]
    
    def get_indexing_stats(self) -> Dict[str, Any]:
        """Get statistics about indexed content."""
        with self._get_connection() as conn:
            # Total videos
            cursor = conn.execute("SELECT COUNT(*) as total FROM videos")
            total_videos = cursor.fetchone()['total']
            
            # Indexed videos
            cursor = conn.execute("SELECT COUNT(*) as indexed FROM videos WHERE indexed = TRUE")
            indexed_videos = cursor.fetchone()['indexed']
            
            # Total segments
            cursor = conn.execute("SELECT COUNT(*) as total FROM video_segments")
            total_segments = cursor.fetchone()['total']
            
            # Total duration
            cursor = conn.execute("SELECT SUM(duration) as total_duration FROM videos WHERE indexed = TRUE")
            total_duration = cursor.fetchone()['total_duration'] or 0
            
            return {
                'total_videos': total_videos,
                'indexed_videos': indexed_videos,
                'total_segments': total_segments,
                'total_duration_hours': total_duration / 3600,
                'indexing_progress': indexed_videos / total_videos if total_videos > 0 else 0
            }
