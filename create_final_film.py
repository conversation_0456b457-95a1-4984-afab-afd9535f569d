"""
Create the final experimental film with advanced effects and audio.
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_final_experimental_film():
    """Create a comprehensive experimental film."""
    
    logger.info("🎬 Creating Final Experimental Film...")
    
    try:
        # Import all effects
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        from effects.recursive_effects import RecursiveEffects
        from video_analysis.frame_extractor import FrameExtractor
        
        # Initialize effects
        color_fx = ColorEffects()
        warp_fx = FrameWarpingEffect()
        glitch_fx = GlitchEffects()
        datamosh_fx = DatamoshEffect()
        recursive_fx = RecursiveEffects()
        extractor = FrameExtractor()
        
        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if len(video_files) < 2:
            logger.error("Need at least 2 videos for experimental film")
            return False
        
        logger.info(f"Found {len(video_files)} source videos")
        
        # Output settings
        output_path = Path("./output/final_experimental_film.mp4")
        output_path.parent.mkdir(exist_ok=True)
        
        fps = 24
        resolution = (1280, 720)
        target_duration = 45  # 45 seconds
        target_frames = fps * target_duration
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, resolution)
        
        if not out.isOpened():
            logger.error("Failed to create video writer")
            return False
        
        logger.info(f"Target: {target_frames} frames ({target_duration}s at {fps}fps)")
        
        # Define experimental sequences
        sequences = [
            {
                'name': 'Opening Glitch',
                'duration': 8,
                'effects': ['digital_noise', 'rgb_shift', 'scan_lines'],
                'style': 'aggressive'
            },
            {
                'name': 'Color Dreams',
                'duration': 10,
                'effects': ['color_bleed', 'chromatic_aberration', 'false_color'],
                'style': 'dreamy'
            },
            {
                'name': 'Warped Reality',
                'duration': 12,
                'effects': ['spiral_distortion', 'kaleidoscope', 'wave_distortion'],
                'style': 'surreal'
            },
            {
                'name': 'Recursive Madness',
                'duration': 10,
                'effects': ['recursive_overlay', 'fractal_feedback', 'recursive_rotation'],
                'style': 'complex'
            },
            {
                'name': 'Final Chaos',
                'duration': 5,
                'effects': ['block_corruption', 'vhs_artifacts', 'bit_crush'],
                'style': 'chaotic'
            }
        ]
        
        total_frames_written = 0
        
        for seq_idx, sequence in enumerate(sequences):
            logger.info(f"Creating sequence {seq_idx + 1}: {sequence['name']}")
            
            # Select video for this sequence
            video_file = video_files[seq_idx % len(video_files)]
            
            # Extract frames for this sequence
            start_time = random.uniform(0, 20)  # Random start within first 20 seconds
            end_time = start_time + sequence['duration'] + 2  # Extra buffer
            
            frames = extractor.extract_frames(video_file, start_time=start_time, end_time=end_time)
            
            if not frames:
                logger.warning(f"No frames extracted for sequence {sequence['name']}")
                continue
            
            # Convert to frame list
            frame_list = [frame for _, frame in frames]
            
            # Apply sequence-specific effects
            processed_frames = apply_sequence_effects(
                frame_list, sequence['effects'], sequence['style'],
                color_fx, warp_fx, glitch_fx, datamosh_fx, recursive_fx
            )
            
            # Calculate frames needed for this sequence
            sequence_frames = int(sequence['duration'] * fps)
            
            # Adjust frame count
            if len(processed_frames) > sequence_frames:
                # Subsample frames
                indices = np.linspace(0, len(processed_frames) - 1, sequence_frames, dtype=int)
                processed_frames = [processed_frames[i] for i in indices]
            elif len(processed_frames) < sequence_frames:
                # Repeat frames to fill duration
                while len(processed_frames) < sequence_frames:
                    processed_frames.extend(processed_frames[:min(len(processed_frames), 
                                                                sequence_frames - len(processed_frames))])
            
            # Write frames
            for frame in processed_frames:
                if total_frames_written >= target_frames:
                    break
                
                # Resize and write
                frame_resized = cv2.resize(frame, resolution)
                out.write(frame_resized)
                total_frames_written += 1
                
                if total_frames_written % 100 == 0:
                    logger.info(f"Progress: {total_frames_written}/{target_frames} frames "
                              f"({total_frames_written/target_frames*100:.1f}%)")
            
            if total_frames_written >= target_frames:
                break
        
        # Fill any remaining frames with abstract content
        if total_frames_written < target_frames:
            logger.info("Creating abstract ending...")
            remaining_frames = target_frames - total_frames_written
            
            for i in range(remaining_frames):
                # Create abstract frame
                abstract_frame = create_abstract_frame(resolution, i, remaining_frames)
                out.write(abstract_frame)
                total_frames_written += 1
        
        # Release video writer
        out.release()
        
        # Verify output
        if output_path.exists():
            file_size = output_path.stat().st_size / 1024 / 1024
            logger.info(f"✅ Final experimental film created: {output_path}")
            logger.info(f"Duration: {total_frames_written / fps:.1f}s")
            logger.info(f"File size: {file_size:.1f} MB")
            logger.info(f"Resolution: {resolution[0]}x{resolution[1]}")
            logger.info(f"FPS: {fps}")
            
            # Create a summary
            create_film_summary(output_path, sequences, total_frames_written, fps)
            
            return True
        else:
            logger.error("❌ Final film was not created")
            return False
        
    except Exception as e:
        logger.error(f"Error creating final experimental film: {e}")
        import traceback
        traceback.print_exc()
        return False

def apply_sequence_effects(frames, effects, style, color_fx, warp_fx, glitch_fx, datamosh_fx, recursive_fx):
    """Apply effects based on sequence style."""
    
    processed_frames = frames.copy()
    
    # Apply effects based on style
    if style == 'aggressive':
        # High intensity effects
        for effect in effects:
            if effect == 'digital_noise':
                processed_frames = [glitch_fx.apply_digital_noise(f, intensity=0.4) for f in processed_frames]
            elif effect == 'rgb_shift':
                processed_frames = [glitch_fx.apply_rgb_shift(f, shift_amount=8) for f in processed_frames]
            elif effect == 'scan_lines':
                processed_frames = [glitch_fx.apply_scan_lines(f, intensity=0.8) for f in processed_frames]
        
        # Apply datamosh for chaos
        if len(processed_frames) > 3:
            processed_frames = datamosh_fx.apply_datamosh(processed_frames, intensity=0.6)
    
    elif style == 'dreamy':
        # Smooth, flowing effects
        for effect in effects:
            if effect == 'color_bleed':
                processed_frames = [color_fx.apply_color_bleed(f, intensity=0.6) for f in processed_frames]
            elif effect == 'chromatic_aberration':
                processed_frames = [color_fx.apply_chromatic_aberration(f, intensity=0.5) for f in processed_frames]
            elif effect == 'false_color':
                processed_frames = [color_fx.apply_false_color(f, color_map="rainbow") for f in processed_frames]
    
    elif style == 'surreal':
        # Warping and distortion
        for effect in effects:
            if effect == 'spiral_distortion':
                processed_frames = [warp_fx.apply_spiral_distortion(f, intensity=0.4) for f in processed_frames]
            elif effect == 'kaleidoscope':
                processed_frames = [warp_fx.apply_kaleidoscope(f, segments=6) for f in processed_frames]
            elif effect == 'wave_distortion':
                processed_frames = [warp_fx.apply_wave_distortion(f, amplitude=25) for f in processed_frames]
    
    elif style == 'complex':
        # Recursive and feedback effects
        for effect in effects:
            if effect == 'recursive_overlay':
                processed_frames = [recursive_fx.apply_recursive_overlay(f) for f in processed_frames]
            elif effect == 'fractal_feedback':
                processed_frames = [recursive_fx.apply_fractal_feedback(f) for f in processed_frames]
            elif effect == 'recursive_rotation':
                processed_frames = [recursive_fx.apply_recursive_rotation(f) for f in processed_frames]
        
        # Apply temporal effects
        if len(processed_frames) > 5:
            processed_frames = recursive_fx.apply_temporal_echo(processed_frames, echo_frames=3)
    
    elif style == 'chaotic':
        # Maximum chaos
        for effect in effects:
            if effect == 'block_corruption':
                processed_frames = [glitch_fx.apply_block_corruption(f, corruption_rate=0.2) for f in processed_frames]
            elif effect == 'vhs_artifacts':
                processed_frames = [glitch_fx.apply_vhs_artifacts(f) for f in processed_frames]
            elif effect == 'bit_crush':
                processed_frames = [glitch_fx.apply_bit_crush(f, bits=2) for f in processed_frames]
    
    return processed_frames

def create_abstract_frame(resolution, frame_index, total_frames):
    """Create abstract frames for ending."""
    
    width, height = resolution
    
    # Create base noise
    frame = np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)
    
    # Add some structure
    progress = frame_index / total_frames
    
    # Create circular patterns
    center_x, center_y = width // 2, height // 2
    y, x = np.ogrid[:height, :width]
    
    # Distance from center
    distance = np.sqrt((x - center_x)**2 + (y - center_y)**2)
    
    # Create ripple effect
    ripple = np.sin(distance * 0.1 + progress * 10) * 127 + 128
    
    # Blend with noise
    frame[:, :, 0] = (frame[:, :, 0] * 0.3 + ripple * 0.7).astype(np.uint8)
    frame[:, :, 1] = (frame[:, :, 1] * 0.5 + ripple * 0.5).astype(np.uint8)
    frame[:, :, 2] = (frame[:, :, 2] * 0.7 + ripple * 0.3).astype(np.uint8)
    
    return frame

def create_film_summary(output_path, sequences, total_frames, fps):
    """Create a summary of the experimental film."""
    
    summary_path = output_path.parent / "film_summary.txt"
    
    with open(summary_path, "w", encoding="utf-8") as f:
        f.write("🎬 EXPERIMENTAL FILM SUMMARY\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Output: {output_path.name}\n")
        f.write(f"Duration: {total_frames / fps:.1f} seconds\n")
        f.write(f"Frames: {total_frames}\n")
        f.write(f"FPS: {fps}\n")
        f.write(f"Resolution: 1280x720\n\n")
        
        f.write("SEQUENCES:\n")
        f.write("-" * 20 + "\n")
        
        for i, seq in enumerate(sequences, 1):
            f.write(f"{i}. {seq['name']} ({seq['duration']}s)\n")
            f.write(f"   Style: {seq['style']}\n")
            f.write(f"   Effects: {', '.join(seq['effects'])}\n\n")
        
        f.write("EFFECTS USED:\n")
        f.write("-" * 20 + "\n")
        all_effects = set()
        for seq in sequences:
            all_effects.update(seq['effects'])
        
        for effect in sorted(all_effects):
            f.write(f"• {effect.replace('_', ' ').title()}\n")
        
        f.write(f"\nTotal unique effects: {len(all_effects)}\n")
    
    logger.info(f"Created film summary: {summary_path}")

if __name__ == "__main__":
    logger.info("🎭 Starting Final Experimental Film Creation")
    
    if create_final_experimental_film():
        logger.info("🎉 Final experimental film completed successfully!")
        logger.info("Check the output directory for the final film and summary")
    else:
        logger.error("❌ Final experimental film creation failed")
