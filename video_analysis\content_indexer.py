"""
Content indexer that combines frame extraction and LLM analysis to create searchable video indices.
"""

import logging
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
import hashlib
from datetime import datetime

from .frame_extractor import FrameExtractor
from .visual_analyzer import VisualAnalyzer
from video_ingestion.local_video_scanner import VideoMetadata
from storage.video_database import VideoDatabase
from config import config

logger = logging.getLogger(__name__)


@dataclass
class VideoSegment:
    """Represents an analyzed video segment."""
    video_path: str
    start_time: float
    end_time: float
    duration: float
    description: str
    style_analysis: str
    content_analysis: str
    emotion_analysis: str
    keyframes_count: int
    analysis_timestamp: datetime
    content_hash: str


class ContentIndexer:
    """Indexes video content using LLM-based analysis."""
    
    def __init__(self, llm_provider: str = None):
        self.frame_extractor = FrameExtractor()
        self.visual_analyzer = VisualAnalyzer(provider=llm_provider)
        self.database = VideoDatabase()
        self.segment_duration = 30.0  # Analyze in 30-second segments
        
    def index_video(self, video_metadata: VideoMetadata, force_reindex: bool = False) -> bool:
        """
        Index a video by analyzing its content in segments.
        
        Args:
            video_metadata: Video metadata object
            force_reindex: Whether to reindex even if already processed
            
        Returns:
            True if indexing was successful
        """
        video_path = Path(video_metadata.path)
        
        # Check if already indexed
        if not force_reindex and self.database.is_video_indexed(video_path):
            logger.info(f"Video already indexed: {video_path}")
            return True
        
        logger.info(f"Starting indexing for: {video_path}")
        
        try:
            # Calculate content hash for change detection
            content_hash = self._calculate_video_hash(video_path)
            
            # Store video metadata
            self.database.store_video_metadata(video_metadata, content_hash)
            
            # Analyze video in segments
            segments = self._create_segments(video_metadata.duration)
            
            for segment_start, segment_end in segments:
                segment = self._analyze_segment(
                    video_path, 
                    segment_start, 
                    segment_end,
                    content_hash
                )
                
                if segment:
                    self.database.store_video_segment(segment)
                    logger.debug(f"Indexed segment {segment_start:.1f}s-{segment_end:.1f}s")
            
            # Mark video as indexed
            self.database.mark_video_indexed(video_path, content_hash)
            
            logger.info(f"Successfully indexed {len(segments)} segments for: {video_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error indexing video {video_path}: {e}")
            return False
    
    def _create_segments(self, duration: float) -> List[tuple]:
        """Create time segments for analysis."""
        segments = []
        current_time = 0.0
        
        while current_time < duration:
            segment_end = min(current_time + self.segment_duration, duration)
            segments.append((current_time, segment_end))
            current_time = segment_end
        
        return segments
    
    def _analyze_segment(self, video_path: Path, start_time: float, end_time: float, content_hash: str) -> Optional[VideoSegment]:
        """
        Analyze a video segment using LLM vision.
        
        Args:
            video_path: Path to video file
            start_time: Segment start time
            end_time: Segment end time
            content_hash: Video content hash
            
        Returns:
            VideoSegment object or None if analysis failed
        """
        try:
            # Extract frames from segment
            frames = self.frame_extractor.extract_frames(video_path, start_time, end_time)
            
            if not frames:
                logger.warning(f"No frames extracted for segment {start_time:.1f}s-{end_time:.1f}s")
                return None
            
            # Extract keyframes for more detailed analysis
            keyframes = self.frame_extractor.extract_keyframes(video_path)
            segment_keyframes = [
                (ts, frame) for ts, frame in keyframes 
                if start_time <= ts <= end_time
            ]
            
            # Analyze representative frames
            analysis_frames = []
            
            # Use keyframes if available, otherwise use regular frames
            if segment_keyframes:
                analysis_frames = [frame for _, frame in segment_keyframes[:3]]  # Max 3 keyframes
            else:
                # Use evenly spaced frames
                frame_indices = [0, len(frames)//2, len(frames)-1] if len(frames) > 2 else [0]
                analysis_frames = [frames[i][1] for i in frame_indices if i < len(frames)]
            
            if not analysis_frames:
                return None
            
            # Perform different types of analysis
            comprehensive_analysis = self.visual_analyzer.analyze_frame(analysis_frames[0], "comprehensive")
            style_analysis = self.visual_analyzer.analyze_frame(analysis_frames[0], "style")
            
            # If multiple frames, analyze content and emotion on different frames
            if len(analysis_frames) > 1:
                content_analysis = self.visual_analyzer.analyze_frame(analysis_frames[1], "content")
                emotion_analysis = self.visual_analyzer.analyze_frame(analysis_frames[-1], "emotion")
            else:
                content_analysis = self.visual_analyzer.analyze_frame(analysis_frames[0], "content")
                emotion_analysis = self.visual_analyzer.analyze_frame(analysis_frames[0], "emotion")
            
            # Create segment object
            segment = VideoSegment(
                video_path=str(video_path),
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                description=comprehensive_analysis.get("description", ""),
                style_analysis=style_analysis.get("description", ""),
                content_analysis=content_analysis.get("description", ""),
                emotion_analysis=emotion_analysis.get("description", ""),
                keyframes_count=len(segment_keyframes),
                analysis_timestamp=datetime.now(),
                content_hash=content_hash
            )
            
            return segment
            
        except Exception as e:
            logger.error(f"Error analyzing segment {start_time:.1f}s-{end_time:.1f}s of {video_path}: {e}")
            return None
    
    def _calculate_video_hash(self, video_path: Path) -> str:
        """Calculate a hash of the video file for change detection."""
        try:
            # Use file size and modification time for a quick hash
            stat = video_path.stat()
            hash_input = f"{video_path.name}_{stat.st_size}_{stat.st_mtime}"
            return hashlib.md5(hash_input.encode()).hexdigest()
        except Exception as e:
            logger.warning(f"Could not calculate hash for {video_path}: {e}")
            return hashlib.md5(str(video_path).encode()).hexdigest()
    
    def get_video_segments(self, video_path: Path) -> List[VideoSegment]:
        """Get all indexed segments for a video."""
        return self.database.get_video_segments(video_path)
    
    def search_segments(self, query: str, limit: int = 10) -> List[VideoSegment]:
        """Search for segments matching a query."""
        return self.database.search_segments(query, limit)
    
    def get_indexing_stats(self) -> Dict[str, Any]:
        """Get statistics about indexed content."""
        return self.database.get_indexing_stats()
