"""
Audio effects for experimental video soundtracks.
"""

import logging
import numpy as np
import librosa
import soundfile as sf
from pathlib import Path
from typing import Tuple, Optional, List
import random

logger = logging.getLogger(__name__)


class AudioEffects:
    """Implements various audio effects for experimental soundtracks."""
    
    def __init__(self, sample_rate: int = 44100):
        self.sample_rate = sample_rate
    
    def load_audio(self, audio_path: Path) -> Tuple[np.ndarray, int]:
        """
        Load audio file.
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Tuple of (audio_data, sample_rate)
        """
        try:
            audio, sr = librosa.load(str(audio_path), sr=self.sample_rate)
            return audio, sr
        except Exception as e:
            logger.error(f"Error loading audio {audio_path}: {e}")
            return np.array([]), self.sample_rate
    
    def save_audio(self, audio: np.ndarray, output_path: Path, sample_rate: int = None) -> bool:
        """
        Save audio to file.
        
        Args:
            audio: Audio data
            output_path: Output file path
            sample_rate: Sample rate
            
        Returns:
            True if successful
        """
        try:
            sr = sample_rate or self.sample_rate
            sf.write(str(output_path), audio, sr)
            return True
        except Exception as e:
            logger.error(f"Error saving audio to {output_path}: {e}")
            return False
    
    def apply_reverse(self, audio: np.ndarray) -> np.ndarray:
        """
        Reverse audio.
        
        Args:
            audio: Input audio
            
        Returns:
            Reversed audio
        """
        return np.flip(audio)
    
    def apply_pitch_shift(self, audio: np.ndarray, semitones: float) -> np.ndarray:
        """
        Apply pitch shifting.
        
        Args:
            audio: Input audio
            semitones: Number of semitones to shift
            
        Returns:
            Pitch-shifted audio
        """
        try:
            return librosa.effects.pitch_shift(audio, sr=self.sample_rate, n_steps=semitones)
        except Exception as e:
            logger.warning(f"Pitch shift failed: {e}")
            return audio
    
    def apply_time_stretch(self, audio: np.ndarray, rate: float) -> np.ndarray:
        """
        Apply time stretching.
        
        Args:
            audio: Input audio
            rate: Stretch rate (>1 = faster, <1 = slower)
            
        Returns:
            Time-stretched audio
        """
        try:
            return librosa.effects.time_stretch(audio, rate=rate)
        except Exception as e:
            logger.warning(f"Time stretch failed: {e}")
            return audio
    
    def apply_granular_synthesis(self, audio: np.ndarray, grain_size: int = 1024,
                                overlap: float = 0.5, randomize: bool = True) -> np.ndarray:
        """
        Apply granular synthesis effect.
        
        Args:
            audio: Input audio
            grain_size: Size of grains in samples
            overlap: Overlap between grains (0-1)
            randomize: Whether to randomize grain order
            
        Returns:
            Granular synthesis audio
        """
        if len(audio) < grain_size:
            return audio
        
        hop_size = int(grain_size * (1 - overlap))
        grains = []
        
        # Extract grains
        for i in range(0, len(audio) - grain_size, hop_size):
            grain = audio[i:i + grain_size]
            grains.append(grain)
        
        if not grains:
            return audio
        
        # Randomize grain order if requested
        if randomize:
            random.shuffle(grains)
        
        # Reconstruct audio
        result = np.zeros(len(audio))
        pos = 0
        
        for grain in grains:
            if pos + len(grain) <= len(result):
                # Apply window to grain
                window = np.hanning(len(grain))
                windowed_grain = grain * window
                
                # Add to result with overlap
                result[pos:pos + len(grain)] += windowed_grain
                pos += hop_size
            else:
                break
        
        # Normalize
        if np.max(np.abs(result)) > 0:
            result = result / np.max(np.abs(result))
        
        return result
    
    def apply_spectral_filtering(self, audio: np.ndarray, filter_type: str = "random") -> np.ndarray:
        """
        Apply spectral filtering.
        
        Args:
            audio: Input audio
            filter_type: Type of filter ("lowpass", "highpass", "bandpass", "random")
            
        Returns:
            Filtered audio
        """
        try:
            # Compute STFT
            stft = librosa.stft(audio)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # Apply filtering
            if filter_type == "lowpass":
                # Keep only low frequencies
                cutoff = magnitude.shape[0] // 3
                magnitude[cutoff:, :] *= 0.1
            elif filter_type == "highpass":
                # Keep only high frequencies
                cutoff = magnitude.shape[0] // 3
                magnitude[:cutoff, :] *= 0.1
            elif filter_type == "bandpass":
                # Keep only middle frequencies
                low_cutoff = magnitude.shape[0] // 4
                high_cutoff = 3 * magnitude.shape[0] // 4
                magnitude[:low_cutoff, :] *= 0.1
                magnitude[high_cutoff:, :] *= 0.1
            elif filter_type == "random":
                # Random spectral filtering
                mask = np.random.random(magnitude.shape) > 0.3
                magnitude *= mask
            
            # Reconstruct audio
            filtered_stft = magnitude * np.exp(1j * phase)
            filtered_audio = librosa.istft(filtered_stft)
            
            return filtered_audio
            
        except Exception as e:
            logger.warning(f"Spectral filtering failed: {e}")
            return audio
    
    def apply_convolution_reverb(self, audio: np.ndarray, impulse_response: Optional[np.ndarray] = None) -> np.ndarray:
        """
        Apply convolution reverb.
        
        Args:
            audio: Input audio
            impulse_response: Impulse response for convolution
            
        Returns:
            Reverb audio
        """
        if impulse_response is None:
            # Create synthetic impulse response
            impulse_response = self._create_synthetic_impulse_response()
        
        try:
            # Apply convolution
            reverb_audio = np.convolve(audio, impulse_response, mode='same')
            
            # Normalize
            if np.max(np.abs(reverb_audio)) > 0:
                reverb_audio = reverb_audio / np.max(np.abs(reverb_audio))
            
            return reverb_audio
            
        except Exception as e:
            logger.warning(f"Convolution reverb failed: {e}")
            return audio
    
    def _create_synthetic_impulse_response(self, length: int = 8192) -> np.ndarray:
        """Create a synthetic impulse response."""
        # Create exponentially decaying noise
        decay = np.exp(-np.arange(length) / (length * 0.3))
        noise = np.random.normal(0, 1, length)
        impulse = noise * decay
        
        # Normalize
        if np.max(np.abs(impulse)) > 0:
            impulse = impulse / np.max(np.abs(impulse))
        
        return impulse
    
    def apply_bitcrushing(self, audio: np.ndarray, bits: int = 8) -> np.ndarray:
        """
        Apply bitcrushing effect.
        
        Args:
            audio: Input audio
            bits: Number of bits
            
        Returns:
            Bitcrushed audio
        """
        # Quantize audio
        max_val = 2 ** (bits - 1)
        quantized = np.round(audio * max_val) / max_val
        
        return np.clip(quantized, -1, 1)
    
    def apply_ring_modulation(self, audio: np.ndarray, frequency: float = 440) -> np.ndarray:
        """
        Apply ring modulation.
        
        Args:
            audio: Input audio
            frequency: Modulation frequency
            
        Returns:
            Ring modulated audio
        """
        # Create modulation signal
        t = np.arange(len(audio)) / self.sample_rate
        modulator = np.sin(2 * np.pi * frequency * t)
        
        # Apply ring modulation
        modulated = audio * modulator
        
        return modulated
    
    def apply_frequency_shifting(self, audio: np.ndarray, shift_hz: float = 100) -> np.ndarray:
        """
        Apply frequency shifting.
        
        Args:
            audio: Input audio
            shift_hz: Frequency shift in Hz
            
        Returns:
            Frequency-shifted audio
        """
        try:
            # Compute STFT
            stft = librosa.stft(audio)
            
            # Shift frequencies
            freq_bins = stft.shape[0]
            shift_bins = int(shift_hz * freq_bins / (self.sample_rate / 2))
            
            shifted_stft = np.zeros_like(stft)
            
            if shift_bins > 0:
                shifted_stft[shift_bins:, :] = stft[:-shift_bins, :]
            elif shift_bins < 0:
                shifted_stft[:shift_bins, :] = stft[-shift_bins:, :]
            else:
                shifted_stft = stft
            
            # Reconstruct audio
            shifted_audio = librosa.istft(shifted_stft)
            
            return shifted_audio
            
        except Exception as e:
            logger.warning(f"Frequency shifting failed: {e}")
            return audio
    
    def create_ambient_texture(self, duration: float, base_frequency: float = 220) -> np.ndarray:
        """
        Create ambient texture for experimental soundtracks.
        
        Args:
            duration: Duration in seconds
            base_frequency: Base frequency for texture
            
        Returns:
            Ambient texture audio
        """
        samples = int(duration * self.sample_rate)
        t = np.arange(samples) / self.sample_rate
        
        # Create multiple sine waves with slight detuning
        texture = np.zeros(samples)
        
        for i in range(5):
            freq = base_frequency * (1 + i * 0.1 + np.random.normal(0, 0.02))
            amplitude = 0.2 / (i + 1)
            phase = np.random.uniform(0, 2 * np.pi)
            
            # Add slow amplitude modulation
            mod_freq = 0.1 + np.random.uniform(0, 0.2)
            mod_depth = 0.3
            amplitude_mod = 1 + mod_depth * np.sin(2 * np.pi * mod_freq * t)
            
            wave = amplitude * amplitude_mod * np.sin(2 * np.pi * freq * t + phase)
            texture += wave
        
        # Add filtered noise
        noise = np.random.normal(0, 0.1, samples)
        filtered_noise = self.apply_spectral_filtering(noise, "lowpass")
        texture += filtered_noise * 0.3
        
        # Apply envelope
        envelope = np.ones(samples)
        fade_samples = int(0.1 * self.sample_rate)  # 0.1 second fade
        
        # Fade in
        envelope[:fade_samples] = np.linspace(0, 1, fade_samples)
        # Fade out
        envelope[-fade_samples:] = np.linspace(1, 0, fade_samples)
        
        texture *= envelope
        
        # Normalize
        if np.max(np.abs(texture)) > 0:
            texture = texture / np.max(np.abs(texture)) * 0.7
        
        return texture
    
    def sync_audio_to_video_effects(self, audio: np.ndarray, effect_timestamps: List[float],
                                  effect_types: List[str]) -> np.ndarray:
        """
        Synchronize audio effects with video effects.
        
        Args:
            audio: Input audio
            effect_timestamps: List of timestamps for effects
            effect_types: List of effect types
            
        Returns:
            Audio with synchronized effects
        """
        result = audio.copy()
        
        for timestamp, effect_type in zip(effect_timestamps, effect_types):
            start_sample = int(timestamp * self.sample_rate)
            
            if start_sample < len(result):
                # Apply effect based on type
                if effect_type == "glitch":
                    # Apply bitcrushing for glitch effects
                    end_sample = min(start_sample + int(0.5 * self.sample_rate), len(result))
                    segment = result[start_sample:end_sample]
                    result[start_sample:end_sample] = self.apply_bitcrushing(segment, bits=4)
                
                elif effect_type == "datamosh":
                    # Apply granular synthesis for datamosh
                    end_sample = min(start_sample + int(1.0 * self.sample_rate), len(result))
                    segment = result[start_sample:end_sample]
                    result[start_sample:end_sample] = self.apply_granular_synthesis(segment, randomize=True)
                
                elif effect_type == "color_bleed":
                    # Apply frequency shifting for color effects
                    end_sample = min(start_sample + int(0.3 * self.sample_rate), len(result))
                    segment = result[start_sample:end_sample]
                    result[start_sample:end_sample] = self.apply_frequency_shifting(segment, shift_hz=50)
        
        return result
