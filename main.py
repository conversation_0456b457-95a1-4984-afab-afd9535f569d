"""
Main entry point for the LLM-driven Experimental Film Pipeline.
"""

import click
import logging
from pathlib import Path
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>

from config import config
from video_ingestion.youtube_downloader import YouTubeDownloader
from video_ingestion.local_video_scanner import LocalVideoScanner
from video_analysis.content_indexer import ContentIndexer
from search.semantic_search import SemanticSearch
from creative.film_planner import FilmPlanner
from editing.video_compositor import VideoCompositor

# Setup logging
logging.basicConfig(
    level=getattr(logging, config.log_level),
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True)]
)

console = Console()
logger = logging.getLogger(__name__)


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug mode')
def cli(debug):
    """LLM-driven Experimental Film Pipeline."""
    if debug:
        config.debug = True
        logging.getLogger().setLevel(logging.DEBUG)


@cli.command()
@click.option('--url', help='YouTube URL to download')
@click.option('--playlist', help='YouTube playlist URL')
@click.option('--output-dir', type=click.Path(), help='Output directory')
def download(url, playlist, output_dir):
    """Download videos from YouTube."""
    console.print("[bold blue]Starting YouTube download...[/bold blue]")
    
    downloader = YouTubeDownloader()
    
    if url:
        result = downloader.download_video(url, output_dir)
        console.print(f"[green]Downloaded: {result}[/green]")
    elif playlist:
        results = downloader.download_playlist(playlist, output_dir)
        console.print(f"[green]Downloaded {len(results)} videos[/green]")
    else:
        console.print("[red]Please provide either --url or --playlist[/red]")


@cli.command()
@click.option('--input-dir', type=click.Path(exists=True), help='Directory to scan for videos')
@click.option('--reindex', is_flag=True, help='Force reindexing of all videos')
def index(input_dir, reindex):
    """Index videos for semantic search."""
    console.print("[bold blue]Starting video indexing...[/bold blue]")
    
    # Scan for videos
    scanner = LocalVideoScanner()
    video_files = scanner.scan_directory(input_dir or config.video.input_dir)
    
    console.print(f"Found {len(video_files)} video files")
    
    # Index videos
    indexer = ContentIndexer()
    for video_file in video_files:
        console.print(f"Indexing: {video_file.name}")
        indexer.index_video(video_file, force_reindex=reindex)
    
    console.print("[green]Indexing complete![/green]")


@cli.command()
@click.argument('query')
@click.option('--limit', default=10, help='Maximum number of results')
def search(query, limit):
    """Search for video segments using natural language."""
    console.print(f"[bold blue]Searching for: {query}[/bold blue]")
    
    search_engine = SemanticSearch()
    results = search_engine.search(query, limit=limit)
    
    console.print(f"\n[green]Found {len(results)} results:[/green]")
    for i, result in enumerate(results, 1):
        console.print(f"{i}. {result.video_path} ({result.start_time:.1f}s - {result.end_time:.1f}s)")
        console.print(f"   Score: {result.similarity_score:.3f}")
        console.print(f"   Description: {result.description}")
        console.print()


@cli.command()
@click.argument('concept')
@click.option('--duration', default=60, help='Target duration in seconds')
@click.option('--style', help='Experimental style (e.g., "glitch", "datamosh", "surreal")')
def create(concept, duration, style):
    """Create an experimental film based on a concept."""
    console.print(f"[bold blue]Creating experimental film: {concept}[/bold blue]")
    
    # Plan the film
    planner = FilmPlanner()
    film_plan = planner.create_plan(concept, duration, style)
    
    console.print(f"[yellow]Film plan created with {len(film_plan.segments)} segments[/yellow]")
    
    # Search for matching content
    search_engine = SemanticSearch()
    compositor = VideoCompositor()
    
    for segment in film_plan.segments:
        console.print(f"Processing segment: {segment.description}")
        
        # Find matching video content
        results = search_engine.search(segment.search_query, limit=5)
        
        if results:
            # Apply effects and add to composition
            best_result = results[0]
            compositor.add_segment(
                video_path=best_result.video_path,
                start_time=best_result.start_time,
                end_time=best_result.end_time,
                effects=segment.effects,
                target_duration=segment.duration
            )
    
    # Render final video
    output_path = config.video.output_dir / f"{concept.replace(' ', '_')}_experimental.mp4"
    compositor.render(output_path)
    
    console.print(f"[green]Experimental film created: {output_path}[/green]")


@cli.command()
def web():
    """Start the web interface."""
    console.print("[bold blue]Starting web interface...[/bold blue]")
    
    try:
        import uvicorn
        from web.app import app
        
        uvicorn.run(
            app,
            host=config.web_host,
            port=config.web_port,
            log_level=config.log_level.lower()
        )
    except ImportError:
        console.print("[red]Web interface dependencies not installed. Run: pip install fastapi uvicorn[/red]")


if __name__ == "__main__":
    cli()
