"""
Test the LLM-driven film planner with Gemini function calling.
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_llm_film_planner():
    """Test the LLM film planner with real video content."""
    
    logger.info("🤖 Testing LLM-Driven Film Planner")
    
    # Check if Gemini API key is available
    if not os.getenv("GEMINI_API_KEY"):
        logger.error("GEMINI_API_KEY not found in environment variables")
        logger.info("Please add your Gemini API key to the .env file")
        return False
    
    try:
        from llm_film_planner import LLMFilmPlanner
        from video_analysis.frame_extractor import FrameExtractor
        
        # Initialize planner
        planner = LLMFilmPlanner()
        extractor = FrameExtractor()
        
        # Get source video
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found")
            return False
        
        source_video = video_files[0]
        logger.info(f"Using source video: {source_video.name}")
        
        # Extract frames for processing
        logger.info("Extracting frames...")
        frames = extractor.extract_frames(source_video, start_time=0, end_time=5)
        
        if len(frames) < 5:
            logger.error("Not enough frames extracted")
            return False
        
        # Convert to frame list
        frame_list = [frame for _, frame in frames[:20]]  # Use first 20 frames
        logger.info(f"Extracted {len(frame_list)} frames for processing")
        
        # Test different concepts
        concepts = [
            ("digital anxiety", "glitch"),
            ("cosmic dreams", "psychedelic"),
            ("urban decay", "gritty"),
            ("memory fragments", "nostalgic"),
            ("data corruption", "cyberpunk")
        ]
        
        for concept, style in concepts:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎬 Creating plan for: '{concept}' (style: {style})")
            logger.info(f"{'='*60}")
            
            # Create film plan using LLM
            plan = planner.create_experimental_film_plan(concept, style)
            
            if plan.get("error"):
                logger.error(f"Error creating plan: {plan['error']}")
                continue
            
            # Display the plan
            logger.info(f"📋 Plan created for '{concept}':")
            if plan.get("llm_response"):
                logger.info(f"LLM Response: {plan['llm_response'][:200]}...")
            
            logger.info(f"Number of segments: {len(plan['segments'])}")
            
            for i, segment in enumerate(plan["segments"], 1):
                logger.info(f"  Segment {i}: {segment['effect_function']}")
                logger.info(f"    Parameters: {segment['parameters']}")
            
            if not plan["segments"]:
                logger.warning("No segments planned, skipping execution")
                continue
            
            # Execute the plan
            logger.info("🎭 Executing film plan...")
            try:
                processed_frames = planner.execute_film_plan(plan, frame_list.copy())
                
                # Save result as video
                output_path = Path(f"./output/llm_planned_{concept.replace(' ', '_')}.mp4")
                output_path.parent.mkdir(exist_ok=True)
                
                success = save_frames_as_video(processed_frames, output_path)
                
                if success:
                    logger.info(f"✅ Created experimental film: {output_path}")
                else:
                    logger.error(f"❌ Failed to save film: {output_path}")
                
            except Exception as e:
                logger.error(f"Error executing plan: {e}")
                continue
        
        logger.info("\n🎉 LLM Film Planner test completed!")
        return True
        
    except Exception as e:
        logger.error(f"Error in LLM film planner test: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_frames_as_video(frames: list, output_path: Path, fps: int = 24) -> bool:
    """Save frames as video file."""
    
    if not frames:
        return False
    
    try:
        # Get frame dimensions
        height, width = frames[0].shape[:2]
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
        
        if not out.isOpened():
            logger.error("Failed to create video writer")
            return False
        
        # Write frames
        for frame in frames:
            out.write(frame)
        
        out.release()
        
        # Verify file was created
        if output_path.exists():
            file_size = output_path.stat().st_size / 1024 / 1024
            logger.info(f"Video saved: {file_size:.1f} MB, {len(frames)} frames")
            return True
        
        return False
        
    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False

def test_simple_function_calling():
    """Test simple function calling without video processing."""
    
    logger.info("🧪 Testing Simple Function Calling")
    
    if not os.getenv("GEMINI_API_KEY"):
        logger.error("GEMINI_API_KEY not found")
        return False
    
    try:
        from google import genai
        from google.genai import types
        
        # Simple test function
        test_function = {
            "name": "create_effect_plan",
            "description": "Create a plan for applying video effects",
            "parameters": {
                "type": "object",
                "properties": {
                    "effect_name": {
                        "type": "string",
                        "description": "Name of the effect to apply"
                    },
                    "intensity": {
                        "type": "number",
                        "description": "Effect intensity from 0.0 to 1.0"
                    }
                },
                "required": ["effect_name", "intensity"]
            }
        }
        
        # Configure client
        client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
        tools = types.Tool(function_declarations=[test_function])
        config = types.GenerateContentConfig(tools=[tools])
        
        # Test prompt
        prompt = "Create an effect plan for a glitch-style experimental video. Use a digital noise effect with medium intensity."
        
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=prompt,
            config=config
        )
        
        logger.info("✅ Function calling test successful!")
        
        # Check for function calls
        if (response.candidates and 
            response.candidates[0].content.parts):
            
            for part in response.candidates[0].content.parts:
                if hasattr(part, 'function_call') and part.function_call:
                    function_call = part.function_call
                    logger.info(f"Function called: {function_call.name}")
                    logger.info(f"Arguments: {dict(function_call.args)}")
                    return True
        
        # Check for text response
        if hasattr(response, 'text'):
            logger.info(f"Text response: {response.text}")
        
        return True
        
    except Exception as e:
        logger.error(f"Function calling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting LLM Film Planner Tests")
    
    # Test 1: Simple function calling
    logger.info("\n" + "="*50)
    logger.info("TEST 1: Simple Function Calling")
    logger.info("="*50)
    
    if test_simple_function_calling():
        logger.info("✅ Simple function calling test passed")
    else:
        logger.error("❌ Simple function calling test failed")
        exit(1)
    
    # Test 2: Full LLM film planner
    logger.info("\n" + "="*50)
    logger.info("TEST 2: Full LLM Film Planner")
    logger.info("="*50)
    
    if test_llm_film_planner():
        logger.info("✅ LLM film planner test passed")
    else:
        logger.error("❌ LLM film planner test failed")
    
    logger.info("\n🎬 All tests completed!")
    logger.info("Check the output directory for LLM-generated experimental films")
