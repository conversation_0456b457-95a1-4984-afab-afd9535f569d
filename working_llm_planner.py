"""
Working LLM film planner with explicit function calling.
"""

import os
import cv2
import numpy as np
from pathlib import Path
import logging
from dotenv import load_dotenv
from google import genai
from google.genai import types

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WorkingLLMPlanner:
    """Working LLM planner that creates effect sequences."""

    def __init__(self):
        self.client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))

        # Initialize effects
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect

        self.color_fx = ColorEffects()
        self.warp_fx = FrameWarpingEffect()
        self.glitch_fx = GlitchEffects()
        self.datamosh_fx = DatamoshEffect()

    def create_film_plan(self, concept: str, style: str = "experimental") -> dict:
        """Create a film plan using LLM reasoning."""

        prompt = f"""Create an experimental film plan for the concept: "{concept}" with style: "{style}"

You are an experimental filmmaker. Create a detailed plan with 4-6 segments that explore this concept through visual effects.

For each segment, specify:
1. A brief description of what it represents
2. The primary effect to use
3. The intensity/parameters for the effect
4. The mood it should convey

Available effects:
- color_bleed: Separates color channels for psychedelic effects (intensity 0.1-1.0)
- color_shift: Random color channel shifting (no parameters)
- chromatic_aberration: Lens-like color separation (intensity 0.1-1.0)
- wave_distortion: Flowing liquid effects (amplitude 10-50, frequency 0.01-0.2)
- spiral_distortion: Hypnotic swirling (intensity 0.1-1.0)
- kaleidoscope: Symmetrical patterns (segments 3-12)
- digital_noise: Glitch corruption (intensity 0.1-1.0)
- rgb_shift: Channel displacement (shift_amount 1-20)
- scan_lines: TV-like artifacts (intensity 0.1-1.0)
- datamosh: Motion corruption (intensity 0.1-1.0)

Respond with a structured plan in this format:
SEGMENT 1: [description]
Effect: [effect_name]
Parameters: [parameter=value, ...]
Mood: [mood description]

SEGMENT 2: [description]
...

Focus on creating a cohesive visual journey that explores the concept "{concept}" in an experimental way."""

        try:
            response = self.client.models.generate_content(
                model="gemini-1.5-flash",
                contents=prompt
            )

            plan_text = response.text
            logger.info(f"LLM Plan Response:\n{plan_text}")

            # Parse the plan
            segments = self._parse_plan(plan_text)

            return {
                "concept": concept,
                "style": style,
                "plan_text": plan_text,
                "segments": segments
            }

        except Exception as e:
            logger.error(f"Error creating plan: {e}")
            return {"concept": concept, "style": style, "segments": [], "error": str(e)}

    def _parse_plan(self, plan_text: str) -> list:
        """Parse the LLM plan text into structured segments."""

        segments = []
        lines = plan_text.split('\n')
        current_segment = None

        for line in lines:
            line = line.strip()

            if line.startswith('SEGMENT'):
                if current_segment:
                    segments.append(current_segment)

                # Extract description
                description = line.split(':', 1)[1].strip() if ':' in line else "Experimental segment"
                current_segment = {
                    "description": description,
                    "effect": None,
                    "parameters": {},
                    "mood": ""
                }

            elif line.startswith('Effect:') and current_segment:
                effect = line.split(':', 1)[1].strip()
                current_segment["effect"] = effect

            elif line.startswith('Parameters:') and current_segment:
                params_text = line.split(':', 1)[1].strip()
                current_segment["parameters"] = self._parse_parameters(params_text)

            elif line.startswith('Mood:') and current_segment:
                mood = line.split(':', 1)[1].strip()
                current_segment["mood"] = mood

        # Add final segment
        if current_segment:
            segments.append(current_segment)

        return segments

    def _parse_parameters(self, params_text: str) -> dict:
        """Parse parameter text into dictionary."""

        params = {}

        # Simple parsing for common parameters
        if "intensity" in params_text.lower():
            try:
                # Extract number after "intensity"
                import re
                match = re.search(r'intensity[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["intensity"] = float(match.group(1))
            except:
                params["intensity"] = 0.5

        if "amplitude" in params_text.lower():
            try:
                match = re.search(r'amplitude[=\s]*([0-9.]+)', params_text.lower())
                if match:
                    params["amplitude"] = float(match.group(1))
            except:
                params["amplitude"] = 20

        if "segments" in params_text.lower():
            try:
                match = re.search(r'segments[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["segments"] = int(match.group(1))
            except:
                params["segments"] = 6

        if "shift_amount" in params_text.lower():
            try:
                match = re.search(r'shift_amount[=\s]*([0-9]+)', params_text.lower())
                if match:
                    params["shift_amount"] = int(match.group(1))
            except:
                params["shift_amount"] = 5

        return params

    def execute_plan(self, plan: dict, frames: list) -> list:
        """Execute the film plan on the frames."""

        if not plan.get("segments"):
            logger.warning("No segments to execute")
            return frames

        processed_frames = frames.copy()

        logger.info(f"Executing plan with {len(plan['segments'])} segments")

        for i, segment in enumerate(plan["segments"]):
            effect = segment.get("effect", "").lower()
            params = segment.get("parameters", {})

            logger.info(f"Segment {i+1}: {segment['description']}")
            logger.info(f"  Effect: {effect}")
            logger.info(f"  Parameters: {params}")

            try:
                if "color_bleed" in effect:
                    intensity = params.get("intensity", 0.5)
                    processed_frames = [self.color_fx.apply_color_bleed(f, intensity) for f in processed_frames]

                elif "color_shift" in effect:
                    processed_frames = [self.color_fx.apply_color_shift(f) for f in processed_frames]

                elif "chromatic_aberration" in effect:
                    intensity = params.get("intensity", 0.5)
                    processed_frames = [self.color_fx.apply_chromatic_aberration(f, intensity) for f in processed_frames]

                elif "wave_distortion" in effect:
                    amplitude = params.get("amplitude", 20)
                    processed_frames = [self.warp_fx.apply_wave_distortion(f, amplitude) for f in processed_frames]

                elif "spiral_distortion" in effect:
                    intensity = params.get("intensity", 0.5)
                    processed_frames = [self.warp_fx.apply_spiral_distortion(f, intensity) for f in processed_frames]

                elif "kaleidoscope" in effect:
                    segments = params.get("segments", 6)
                    processed_frames = [self.warp_fx.apply_kaleidoscope(f, segments) for f in processed_frames]

                elif "digital_noise" in effect:
                    intensity = params.get("intensity", 0.3)
                    processed_frames = [self.glitch_fx.apply_digital_noise(f, intensity) for f in processed_frames]

                elif "rgb_shift" in effect:
                    shift_amount = params.get("shift_amount", 5)
                    processed_frames = [self.glitch_fx.apply_rgb_shift(f, shift_amount) for f in processed_frames]

                elif "scan_lines" in effect:
                    intensity = params.get("intensity", 0.5)
                    processed_frames = [self.glitch_fx.apply_scan_lines(f, intensity=intensity) for f in processed_frames]

                elif "datamosh" in effect:
                    intensity = params.get("intensity", 0.5)
                    processed_frames = self.datamosh_fx.apply_datamosh(processed_frames, intensity)

                else:
                    logger.warning(f"Unknown effect: {effect}")

                logger.info(f"  ✅ Applied {effect}")

            except Exception as e:
                logger.error(f"  ❌ Error applying {effect}: {e}")

        return processed_frames

def test_working_planner():
    """Test the working LLM planner."""

    logger.info("🤖 Testing Working LLM Planner")

    try:
        # Initialize planner
        planner = WorkingLLMPlanner()

        # Get source frames
        from video_analysis.frame_extractor import FrameExtractor

        extractor = FrameExtractor()
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))

        if not video_files:
            logger.error("No video files found")
            return False

        source_video = video_files[0]
        logger.info(f"Using source video: {source_video.name}")

        # Extract frames
        frames = extractor.extract_frames(source_video, start_time=0, end_time=3)
        frame_list = [frame for _, frame in frames[:15]]  # Use 15 frames

        logger.info(f"Extracted {len(frame_list)} frames")

        # Test concepts
        concepts = [
            ("digital dreams", "psychedelic"),
            ("memory glitch", "nostalgic"),
            ("cyber anxiety", "aggressive")
        ]

        for concept, style in concepts:
            logger.info(f"\n{'='*50}")
            logger.info(f"🎬 Creating plan for: '{concept}' ({style})")
            logger.info(f"{'='*50}")

            # Create plan
            plan = planner.create_film_plan(concept, style)

            if plan.get("error"):
                logger.error(f"Plan creation failed: {plan['error']}")
                continue

            # Execute plan
            processed_frames = planner.execute_plan(plan, frame_list.copy())

            # Save result
            output_path = Path(f"./output/llm_planned_{concept.replace(' ', '_')}.mp4")
            output_path.parent.mkdir(exist_ok=True)

            # Save as video
            if save_frames_as_video(processed_frames, output_path):
                logger.info(f"✅ Created: {output_path}")
            else:
                logger.error(f"❌ Failed to save: {output_path}")

        return True

    except Exception as e:
        logger.error(f"Error in working planner test: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_frames_as_video(frames: list, output_path: Path, fps: int = 24) -> bool:
    """Save frames as video."""

    if not frames:
        return False

    try:
        height, width = frames[0].shape[:2]
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

        if not out.isOpened():
            return False

        for frame in frames:
            out.write(frame)

        out.release()
        return output_path.exists()

    except Exception as e:
        logger.error(f"Error saving video: {e}")
        return False

if __name__ == "__main__":
    test_working_planner()
