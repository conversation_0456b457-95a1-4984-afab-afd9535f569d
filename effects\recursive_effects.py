"""
Recursive and feedback loop effects for experimental video.
"""

import logging
import numpy as np
import cv2
from typing import List, Optional, Tuple
import random
from collections import deque

try:
    from config import config
except ImportError:
    # Fallback config
    class Config:
        class effects:
            recursive_depth = 3
    config = Config()

logger = logging.getLogger(__name__)


class RecursiveEffects:
    """Implements recursive and feedback loop effects."""

    def __init__(self):
        self.recursive_depth = config.effects.recursive_depth
        self.frame_buffer = deque(maxlen=10)  # Buffer for feedback effects

    def apply_video_feedback(self, frames: List[np.ndarray], feedback_strength: float = 0.3,
                           delay_frames: int = 3) -> List[np.ndarray]:
        """
        Apply video feedback effect by mixing current frame with delayed frames.

        Args:
            frames: List of input frames
            feedback_strength: Strength of feedback effect
            delay_frames: Number of frames to delay for feedback

        Returns:
            List of frames with feedback effect
        """
        if not frames:
            return frames

        feedback_frames = []
        frame_buffer = deque(maxlen=delay_frames + 1)

        for i, frame in enumerate(frames):
            # Add current frame to buffer
            frame_buffer.append(frame.copy())

            if len(frame_buffer) > delay_frames:
                # Get delayed frame
                delayed_frame = frame_buffer[0]

                # Mix current frame with delayed frame
                feedback_frame = cv2.addWeighted(
                    frame, 1 - feedback_strength,
                    delayed_frame, feedback_strength, 0
                )
                feedback_frames.append(feedback_frame)
            else:
                # Not enough frames for feedback yet
                feedback_frames.append(frame.copy())

        return feedback_frames

    def apply_recursive_overlay(self, frame: np.ndarray, scale_factor: float = 0.8,
                              offset_x: int = 50, offset_y: int = 50,
                              iterations: int = None) -> np.ndarray:
        """
        Apply recursive overlay effect.

        Args:
            frame: Input frame
            scale_factor: Scale factor for each iteration
            offset_x: X offset for each iteration
            offset_y: Y offset for each iteration
            iterations: Number of recursive iterations

        Returns:
            Frame with recursive overlay
        """
        iterations = iterations or self.recursive_depth
        result = frame.copy().astype(np.float32)

        current_frame = frame.copy()

        for i in range(iterations):
            # Scale down the frame
            height, width = current_frame.shape[:2]
            new_height = int(height * scale_factor)
            new_width = int(width * scale_factor)

            if new_height < 10 or new_width < 10:
                break

            # Resize frame
            scaled_frame = cv2.resize(current_frame, (new_width, new_height))

            # Calculate position for overlay
            pos_x = min(offset_x * (i + 1), width - new_width)
            pos_y = min(offset_y * (i + 1), height - new_height)

            # Create overlay
            overlay = np.zeros_like(result)
            overlay[pos_y:pos_y + new_height, pos_x:pos_x + new_width] = scaled_frame

            # Blend with result
            alpha = 0.5 / (i + 1)  # Decreasing alpha
            result = cv2.addWeighted(result, 1 - alpha, overlay, alpha, 0)

            # Update current frame for next iteration
            current_frame = scaled_frame

        return np.clip(result, 0, 255).astype(np.uint8)

    def apply_infinite_zoom(self, frames: List[np.ndarray], zoom_center: Tuple[float, float] = (0.5, 0.5),
                          zoom_speed: float = 1.02) -> List[np.ndarray]:
        """
        Apply infinite zoom effect across frames.

        Args:
            frames: List of input frames
            zoom_center: Center point for zoom (normalized 0-1)
            zoom_speed: Zoom speed per frame

        Returns:
            List of frames with infinite zoom
        """
        if not frames:
            return frames

        zoomed_frames = []

        for i, frame in enumerate(frames):
            # Calculate zoom factor
            zoom_factor = zoom_speed ** i

            height, width = frame.shape[:2]
            center_x = int(zoom_center[0] * width)
            center_y = int(zoom_center[1] * height)

            # Calculate crop region
            crop_width = int(width / zoom_factor)
            crop_height = int(height / zoom_factor)

            # Ensure minimum crop size
            crop_width = max(10, crop_width)
            crop_height = max(10, crop_height)

            # Calculate crop coordinates
            x1 = max(0, center_x - crop_width // 2)
            y1 = max(0, center_y - crop_height // 2)
            x2 = min(width, x1 + crop_width)
            y2 = min(height, y1 + crop_height)

            # Crop and resize
            cropped = frame[y1:y2, x1:x2]
            zoomed = cv2.resize(cropped, (width, height))

            zoomed_frames.append(zoomed)

        return zoomed_frames

    def apply_temporal_echo(self, frames: List[np.ndarray], echo_frames: int = 5,
                          decay_factor: float = 0.7) -> List[np.ndarray]:
        """
        Apply temporal echo effect by blending with previous frames.

        Args:
            frames: List of input frames
            echo_frames: Number of previous frames to echo
            decay_factor: Decay factor for each echo

        Returns:
            List of frames with temporal echo
        """
        if not frames:
            return frames

        echo_buffer = deque(maxlen=echo_frames)
        echo_result = []

        for frame in frames:
            # Start with current frame
            result = frame.copy().astype(np.float32)

            # Add echoes from buffer
            for i, echo_frame in enumerate(echo_buffer):
                alpha = (decay_factor ** (len(echo_buffer) - i))
                result = cv2.addWeighted(result, 1 - alpha, echo_frame.astype(np.float32), alpha, 0)

            # Add current frame to buffer
            echo_buffer.append(frame.copy())

            echo_result.append(np.clip(result, 0, 255).astype(np.uint8))

        return echo_result

    def apply_fractal_feedback(self, frame: np.ndarray, iterations: int = None) -> np.ndarray:
        """
        Apply fractal-like feedback effect.

        Args:
            frame: Input frame
            iterations: Number of fractal iterations

        Returns:
            Frame with fractal feedback
        """
        iterations = iterations or self.recursive_depth
        result = frame.copy().astype(np.float32)

        height, width = frame.shape[:2]

        for i in range(iterations):
            # Create multiple scaled copies at different positions
            scale = 0.6 ** (i + 1)

            if scale < 0.1:
                break

            new_height = int(height * scale)
            new_width = int(width * scale)

            if new_height < 5 or new_width < 5:
                break

            # Resize frame
            scaled = cv2.resize(result, (new_width, new_height))

            # Place at multiple positions
            positions = [
                (width // 4, height // 4),
                (3 * width // 4, height // 4),
                (width // 4, 3 * height // 4),
                (3 * width // 4, 3 * height // 4),
                (width // 2, height // 2)
            ]

            for pos_x, pos_y in positions:
                # Adjust position to fit scaled frame
                pos_x = min(pos_x, width - new_width)
                pos_y = min(pos_y, height - new_height)

                if pos_x >= 0 and pos_y >= 0:
                    # Create overlay
                    overlay = np.zeros_like(result)
                    overlay[pos_y:pos_y + new_height, pos_x:pos_x + new_width] = scaled

                    # Blend
                    alpha = 0.2 / (i + 1)
                    result = cv2.addWeighted(result, 1 - alpha, overlay, alpha, 0)

        return np.clip(result, 0, 255).astype(np.uint8)

    def apply_recursive_rotation(self, frame: np.ndarray, rotation_angle: float = 15,
                               scale_factor: float = 0.9, iterations: int = None) -> np.ndarray:
        """
        Apply recursive rotation effect.

        Args:
            frame: Input frame
            rotation_angle: Rotation angle per iteration
            scale_factor: Scale factor per iteration
            iterations: Number of iterations

        Returns:
            Frame with recursive rotation
        """
        iterations = iterations or self.recursive_depth
        result = frame.copy().astype(np.float32)

        height, width = frame.shape[:2]
        center = (width // 2, height // 2)

        current_frame = frame.copy()

        for i in range(iterations):
            # Calculate rotation and scale
            angle = rotation_angle * (i + 1)
            scale = scale_factor ** (i + 1)

            if scale < 0.1:
                break

            # Create transformation matrix
            M = cv2.getRotationMatrix2D(center, angle, scale)

            # Apply transformation
            transformed = cv2.warpAffine(current_frame, M, (width, height))

            # Blend with result
            alpha = 0.4 / (i + 1)
            result = cv2.addWeighted(result, 1 - alpha, transformed.astype(np.float32), alpha, 0)

            # Update current frame
            current_frame = transformed

        return np.clip(result, 0, 255).astype(np.uint8)

    def apply_droste_effect(self, frame: np.ndarray, inner_radius: float = 0.2,
                          outer_radius: float = 0.8, iterations: int = None) -> np.ndarray:
        """
        Apply Droste effect (recursive image within image).

        Args:
            frame: Input frame
            inner_radius: Inner radius for recursion (normalized)
            outer_radius: Outer radius for recursion (normalized)
            iterations: Number of recursive iterations

        Returns:
            Frame with Droste effect
        """
        iterations = iterations or self.recursive_depth
        result = frame.copy()

        height, width = frame.shape[:2]
        center_x, center_y = width // 2, height // 2
        max_radius = min(width, height) // 2

        for i in range(iterations):
            # Calculate radii for this iteration
            current_inner = int(inner_radius * max_radius * (0.8 ** i))
            current_outer = int(outer_radius * max_radius * (0.8 ** i))

            if current_inner >= current_outer or current_inner < 5:
                break

            # Create circular mask
            mask = np.zeros((height, width), dtype=np.uint8)
            cv2.circle(mask, (center_x, center_y), current_outer, 255, -1)
            cv2.circle(mask, (center_x, center_y), current_inner, 0, -1)

            # Extract region and resize
            masked_frame = cv2.bitwise_and(result, result, mask=mask)

            # Resize to fit in inner circle
            scale = current_inner / current_outer
            new_size = int(current_outer * 2 * scale)

            if new_size > 0:
                resized = cv2.resize(masked_frame, (new_size, new_size))

                # Place in center
                y_offset = center_y - new_size // 2
                x_offset = center_x - new_size // 2

                if (y_offset >= 0 and x_offset >= 0 and
                    y_offset + new_size <= height and x_offset + new_size <= width):

                    # Create inner mask
                    inner_mask = np.zeros((height, width), dtype=np.uint8)
                    cv2.circle(inner_mask, (center_x, center_y), current_inner, 255, -1)

                    # Blend resized image into inner circle
                    result[y_offset:y_offset + new_size, x_offset:x_offset + new_size] = \
                        cv2.bitwise_and(resized, resized,
                                      mask=inner_mask[y_offset:y_offset + new_size,
                                                    x_offset:x_offset + new_size])

        return result
