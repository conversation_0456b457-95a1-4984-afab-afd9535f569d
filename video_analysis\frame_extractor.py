"""
Frame extraction utilities for video analysis.
"""

import logging
from pathlib import Path
from typing import List, Tuple, Optional
import cv2
import numpy as np
try:
    from config import config
except ImportError:
    # Fallback config
    class Config:
        class video:
            frame_extraction_fps = 1.0
            max_frame_size = (1920, 1080)
            cache_dir = Path("./cache")
    config = Config()

logger = logging.getLogger(__name__)


class FrameExtractor:
    """Extracts frames from videos for analysis."""

    def __init__(self):
        self.extraction_fps = config.video.frame_extraction_fps
        self.max_frame_size = config.video.max_frame_size
        self.cache_dir = config.video.cache_dir / "frames"
        self.cache_dir.mkdir(parents=True, exist_ok=True)

    def extract_frames(self, video_path: Path, start_time: float = 0, end_time: Optional[float] = None) -> List[Tuple[float, np.ndarray]]:
        """
        Extract frames from a video at regular intervals.

        Args:
            video_path: Path to video file
            start_time: Start time in seconds
            end_time: End time in seconds (None for entire video)

        Returns:
            List of (timestamp, frame) tuples
        """
        frames = []

        try:
            cap = cv2.VideoCapture(str(video_path))

            if not cap.isOpened():
                logger.error(f"Could not open video: {video_path}")
                return frames

            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0

            if end_time is None:
                end_time = duration

            # Calculate frame extraction interval
            frame_interval = int(fps / self.extraction_fps) if fps > 0 else 1

            # Set start position
            start_frame = int(start_time * fps)
            end_frame = int(min(end_time * fps, total_frames))

            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

            current_frame = start_frame

            while current_frame < end_frame:
                ret, frame = cap.read()

                if not ret:
                    break

                # Resize frame if necessary
                frame = self._resize_frame(frame)

                # Calculate timestamp
                timestamp = current_frame / fps

                frames.append((timestamp, frame))

                # Skip to next extraction point
                current_frame += frame_interval
                cap.set(cv2.CAP_PROP_POS_FRAMES, current_frame)

            cap.release()

        except Exception as e:
            logger.error(f"Error extracting frames from {video_path}: {e}")

        logger.debug(f"Extracted {len(frames)} frames from {video_path}")
        return frames

    def extract_keyframes(self, video_path: Path, threshold: float = 0.3) -> List[Tuple[float, np.ndarray]]:
        """
        Extract keyframes based on scene changes.

        Args:
            video_path: Path to video file
            threshold: Scene change threshold (0-1)

        Returns:
            List of (timestamp, frame) tuples for keyframes
        """
        keyframes = []

        try:
            cap = cv2.VideoCapture(str(video_path))

            if not cap.isOpened():
                logger.error(f"Could not open video: {video_path}")
                return keyframes

            fps = cap.get(cv2.CAP_PROP_FPS)
            prev_frame = None
            frame_count = 0

            while True:
                ret, frame = cap.read()

                if not ret:
                    break

                # Convert to grayscale for comparison
                gray_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                if prev_frame is not None:
                    # Calculate frame difference
                    diff = cv2.absdiff(prev_frame, gray_frame)
                    diff_score = np.mean(diff) / 255.0

                    # If significant change, consider it a keyframe
                    if diff_score > threshold:
                        timestamp = frame_count / fps
                        resized_frame = self._resize_frame(frame)
                        keyframes.append((timestamp, resized_frame))

                prev_frame = gray_frame
                frame_count += 1

            cap.release()

        except Exception as e:
            logger.error(f"Error extracting keyframes from {video_path}: {e}")

        logger.debug(f"Extracted {len(keyframes)} keyframes from {video_path}")
        return keyframes

    def extract_frame_at_time(self, video_path: Path, timestamp: float) -> Optional[np.ndarray]:
        """
        Extract a single frame at a specific timestamp.

        Args:
            video_path: Path to video file
            timestamp: Time in seconds

        Returns:
            Frame as numpy array or None if failed
        """
        try:
            cap = cv2.VideoCapture(str(video_path))

            if not cap.isOpened():
                return None

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_number = int(timestamp * fps)

            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = cap.read()

            cap.release()

            if ret:
                return self._resize_frame(frame)

        except Exception as e:
            logger.error(f"Error extracting frame at {timestamp}s from {video_path}: {e}")

        return None

    def save_frame(self, frame: np.ndarray, output_path: Path) -> bool:
        """
        Save a frame to disk.

        Args:
            frame: Frame as numpy array
            output_path: Path to save the frame

        Returns:
            True if successful
        """
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            return cv2.imwrite(str(output_path), frame)
        except Exception as e:
            logger.error(f"Error saving frame to {output_path}: {e}")
            return False

    def _resize_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Resize frame to maximum dimensions while maintaining aspect ratio.

        Args:
            frame: Input frame

        Returns:
            Resized frame
        """
        height, width = frame.shape[:2]
        max_width, max_height = self.max_frame_size

        if width <= max_width and height <= max_height:
            return frame

        # Calculate scaling factor
        scale = min(max_width / width, max_height / height)

        new_width = int(width * scale)
        new_height = int(height * scale)

        return cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
