"""
Semantic search interface using LLM function calling for video content discovery.
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import json
from openai import OpenAI
from google import genai
from google.genai import types

from storage.video_database import VideoDatabase
from config import config

logger = logging.getLogger(__name__)


@dataclass
class SearchResult:
    """Represents a search result."""
    video_path: str
    start_time: float
    end_time: float
    duration: float
    description: str
    style_analysis: str
    content_analysis: str
    emotion_analysis: str
    similarity_score: float
    match_reason: str


class SemanticSearch:
    """Semantic search engine using LLM function calling."""

    def __init__(self, provider: str = None):
        self.provider = provider or config.llm.default_provider
        self.database = VideoDatabase()

        if self.provider == "openai":
            self.openai_client = OpenAI(api_key=config.llm.openai_api_key)
        elif self.provider == "gemini":
            self.gemini_client = genai.Client(api_key=config.llm.gemini_api_key)
        else:
            raise ValueError(f"Unsupported LLM provider: {self.provider}")

        # Define search functions for LLM
        self.search_functions = self._define_search_functions()

    def search(self, query: str, limit: int = 10) -> List[SearchResult]:
        """
        Perform semantic search using LLM function calling.

        Args:
            query: Natural language search query
            limit: Maximum number of results

        Returns:
            List of SearchResult objects
        """
        logger.info(f"Searching for: {query}")

        if self.provider == "openai":
            return self._search_with_openai(query, limit)
        elif self.provider == "gemini":
            return self._search_with_gemini(query, limit)

    def _search_with_openai(self, query: str, limit: int) -> List[SearchResult]:
        """Search using OpenAI function calling."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {
                        "role": "system",
                        "content": """You are a video search assistant. Use the provided functions to search for video content based on user queries.
                        Analyze the query to determine the best search strategy and parameters."""
                    },
                    {
                        "role": "user",
                        "content": f"Find video segments matching: {query}"
                    }
                ],
                functions=self.search_functions,
                function_call="auto",
                max_tokens=config.llm.max_tokens,
                temperature=config.llm.temperature
            )

            message = response.choices[0].message

            if message.function_call:
                function_name = message.function_call.name
                function_args = json.loads(message.function_call.arguments)

                logger.debug(f"LLM called function: {function_name} with args: {function_args}")

                # Execute the function
                if function_name == "search_video_content":
                    return self._execute_content_search(function_args, limit)
                elif function_name == "search_by_style":
                    return self._execute_style_search(function_args, limit)
                elif function_name == "search_by_emotion":
                    return self._execute_emotion_search(function_args, limit)
                elif function_name == "combined_search":
                    return self._execute_combined_search(function_args, limit)

            # Fallback to simple text search
            return self._fallback_search(query, limit)

        except Exception as e:
            logger.error(f"OpenAI search failed: {e}")
            return self._fallback_search(query, limit)

    def _search_with_gemini(self, query: str, limit: int) -> List[SearchResult]:
        """Search using Gemini function calling."""

        try:
            # Configure tools for Gemini
            tools = types.Tool(function_declarations=self._get_gemini_functions())
            config_obj = types.GenerateContentConfig(tools=[tools])

            response = self.gemini_client.models.generate_content(
                model=config.llm.gemini_model,
                contents=f"Find video segments matching: {query}",
                config=config_obj
            )

            # Check for function call
            if (response.candidates and
                response.candidates[0].content.parts and
                response.candidates[0].content.parts[0].function_call):

                function_call = response.candidates[0].content.parts[0].function_call
                function_name = function_call.name
                function_args = dict(function_call.args)

                logger.debug(f"Gemini called function: {function_name} with args: {function_args}")

                # Execute the function
                if function_name == "search_video_content":
                    return self._execute_content_search(function_args, limit)
                elif function_name == "search_by_style":
                    return self._execute_style_search(function_args, limit)
                elif function_name == "search_by_emotion":
                    return self._execute_emotion_search(function_args, limit)
                elif function_name == "combined_search":
                    return self._execute_combined_search(function_args, limit)

            # Fallback to simple text search
            return self._fallback_search(query, limit)

        except Exception as e:
            logger.error(f"Gemini search failed: {e}")
            return self._fallback_search(query, limit)

    def _define_search_functions(self) -> List[Dict[str, Any]]:
        """Define search functions for OpenAI."""
        return [
            {
                "name": "search_video_content",
                "description": "Search for video segments based on visual content and objects",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content_keywords": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Keywords describing visual content, objects, or actions"
                        },
                        "setting": {
                            "type": "string",
                            "description": "Setting or environment (e.g., 'urban', 'nature', 'indoor')"
                        }
                    },
                    "required": ["content_keywords"]
                }
            },
            {
                "name": "search_by_style",
                "description": "Search for video segments based on visual style and cinematography",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "style_keywords": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Style keywords (e.g., 'cinematic', 'handheld', 'slow motion')"
                        },
                        "color_palette": {
                            "type": "string",
                            "description": "Color description (e.g., 'warm tones', 'high contrast')"
                        },
                        "experimental_elements": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Experimental or artistic elements"
                        }
                    },
                    "required": ["style_keywords"]
                }
            },
            {
                "name": "search_by_emotion",
                "description": "Search for video segments based on emotional content and mood",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "emotions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Emotions or moods to search for"
                        },
                        "intensity": {
                            "type": "string",
                            "enum": ["low", "medium", "high"],
                            "description": "Emotional intensity level"
                        }
                    },
                    "required": ["emotions"]
                }
            },
            {
                "name": "combined_search",
                "description": "Search using multiple criteria combined",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content_keywords": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Content keywords"
                        },
                        "style_keywords": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Style keywords"
                        },
                        "emotions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Emotional keywords"
                        }
                    }
                }
            }
        ]

    def _get_gemini_functions(self) -> List[Dict[str, Any]]:
        """Get function declarations for Gemini."""
        return [
            {
                "name": "search_video_content",
                "description": "Search for video segments based on visual content and objects",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "content_keywords": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Keywords describing visual content, objects, or actions"
                        },
                        "setting": {
                            "type": "string",
                            "description": "Setting or environment"
                        }
                    },
                    "required": ["content_keywords"]
                }
            }
            # Add other functions as needed
        ]

    def _execute_content_search(self, args: Dict[str, Any], limit: int) -> List[SearchResult]:
        """Execute content-based search."""
        keywords = args.get("content_keywords", [])
        setting = args.get("setting", "")

        # Build search query
        search_terms = keywords + ([setting] if setting else [])
        query = " ".join(search_terms)

        return self._database_search(query, limit, "content")

    def _execute_style_search(self, args: Dict[str, Any], limit: int) -> List[SearchResult]:
        """Execute style-based search."""
        style_keywords = args.get("style_keywords", [])
        color_palette = args.get("color_palette", "")
        experimental = args.get("experimental_elements", [])

        search_terms = style_keywords + ([color_palette] if color_palette else []) + experimental
        query = " ".join(search_terms)

        return self._database_search(query, limit, "style")

    def _execute_emotion_search(self, args: Dict[str, Any], limit: int) -> List[SearchResult]:
        """Execute emotion-based search."""
        emotions = args.get("emotions", [])
        intensity = args.get("intensity", "")

        search_terms = emotions + ([intensity] if intensity else [])
        query = " ".join(search_terms)

        return self._database_search(query, limit, "emotion")

    def _execute_combined_search(self, args: Dict[str, Any], limit: int) -> List[SearchResult]:
        """Execute combined search across all fields."""
        all_terms = []
        all_terms.extend(args.get("content_keywords", []))
        all_terms.extend(args.get("style_keywords", []))
        all_terms.extend(args.get("emotions", []))

        query = " ".join(all_terms)
        return self._database_search(query, limit, "combined")

    def _database_search(self, query: str, limit: int, search_type: str) -> List[SearchResult]:
        """Perform database search and convert to SearchResult objects."""
        segments = self.database.search_segments(query, limit)

        results = []
        for segment in segments:
            result = SearchResult(
                video_path=segment['video_path'],
                start_time=segment['start_time'],
                end_time=segment['end_time'],
                duration=segment['duration'],
                description=segment['description'],
                style_analysis=segment['style_analysis'],
                content_analysis=segment['content_analysis'],
                emotion_analysis=segment['emotion_analysis'],
                similarity_score=1.0 / (segment.get('rank', 1) + 1),  # Convert rank to similarity
                match_reason=f"Matched via {search_type} search"
            )
            results.append(result)

        return results

    def _fallback_search(self, query: str, limit: int) -> List[SearchResult]:
        """Fallback search using simple text matching."""
        logger.info("Using fallback search")
        return self._database_search(query, limit, "fallback")
