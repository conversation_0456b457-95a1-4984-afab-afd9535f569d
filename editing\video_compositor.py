"""
Video compositor for combining segments and applying effects.
"""

import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import cv2
import numpy as np
try:
    from moviepy.editor import VideoFileClip, concatenate_videoclips, CompositeVideoClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    VideoFileClip = None
    concatenate_videoclips = None
    CompositeVideoClip = None
    MOVIEPY_AVAILABLE = False
import tempfile

from effects.datamosh import DatamoshEffect
from effects.color_effects import ColorEffects
from effects.frame_warping import FrameWarpingEffect
from effects.recursive_effects import RecursiveEffects
from effects.glitch_effects import GlitchEffects
from audio.audio_effects import AudioEffects
from audio.audio_analyzer import AudioAnalyzer
from config import config

logger = logging.getLogger(__name__)


@dataclass
class CompositionSegment:
    """Represents a segment in the composition."""
    video_path: Path
    start_time: float
    end_time: float
    effects: List[str]
    target_duration: float
    transition: str = "cut"


class VideoCompositor:
    """Composes final videos from segments with effects."""

    def __init__(self):
        self.segments: List[CompositionSegment] = []
        self.datamosh_effect = DatamoshEffect()
        self.color_effects = ColorEffects()
        self.frame_warping = FrameWarpingEffect()
        self.recursive_effects = RecursiveEffects()
        self.glitch_effects = GlitchEffects()
        self.audio_effects = AudioEffects()
        self.audio_analyzer = AudioAnalyzer()
        self.temp_dir = Path(tempfile.mkdtemp())

    def add_segment(self, video_path: Path, start_time: float, end_time: float,
                   effects: List[str], target_duration: float, transition: str = "cut"):
        """
        Add a segment to the composition.

        Args:
            video_path: Path to source video
            start_time: Start time in source video
            end_time: End time in source video
            effects: List of effects to apply
            target_duration: Target duration for this segment
            transition: Transition type
        """
        segment = CompositionSegment(
            video_path=video_path,
            start_time=start_time,
            end_time=end_time,
            effects=effects,
            target_duration=target_duration,
            transition=transition
        )
        self.segments.append(segment)
        logger.debug(f"Added segment: {video_path} ({start_time:.1f}s-{end_time:.1f}s)")

    def render(self, output_path: Path, fps: int = 30, resolution: tuple = (1920, 1080)) -> bool:
        """
        Render the final composition.

        Args:
            output_path: Output video path
            fps: Target frame rate
            resolution: Target resolution

        Returns:
            True if rendering was successful
        """
        if not MOVIEPY_AVAILABLE:
            logger.error("MoviePy is not available. Cannot render video.")
            return False

        if not self.segments:
            logger.error("No segments to render")
            return False

        logger.info(f"Rendering composition with {len(self.segments)} segments")

        try:
            # Process each segment
            processed_clips = []

            for i, segment in enumerate(self.segments):
                logger.info(f"Processing segment {i+1}/{len(self.segments)}")

                # Extract and process segment
                clip = self._process_segment(segment, fps, resolution)

                if clip:
                    processed_clips.append(clip)
                else:
                    logger.warning(f"Failed to process segment {i+1}")

            if not processed_clips:
                logger.error("No clips were successfully processed")
                return False

            # Concatenate clips
            logger.info("Concatenating clips...")
            final_clip = concatenate_videoclips(processed_clips, method="compose")

            # Write final video
            logger.info(f"Writing final video to {output_path}")
            output_path.parent.mkdir(parents=True, exist_ok=True)

            final_clip.write_videofile(
                str(output_path),
                fps=fps,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile=str(self.temp_dir / 'temp_audio.m4a'),
                remove_temp=True
            )

            # Clean up
            final_clip.close()
            for clip in processed_clips:
                clip.close()

            logger.info(f"Successfully rendered: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error rendering composition: {e}")
            return False

    def _process_segment(self, segment: CompositionSegment, fps: int, resolution: tuple) -> Optional[VideoFileClip]:
        """
        Process a single segment with effects.

        Args:
            segment: Segment to process
            fps: Target frame rate
            resolution: Target resolution

        Returns:
            Processed video clip or None if failed
        """
        try:
            # Load video clip
            clip = VideoFileClip(str(segment.video_path))

            # Extract segment
            segment_clip = clip.subclip(segment.start_time, segment.end_time)

            # Resize to target duration if needed
            if segment.target_duration != segment_clip.duration:
                speed_factor = segment_clip.duration / segment.target_duration
                segment_clip = segment_clip.fx(lambda clip: clip.speedx(speed_factor))

            # Resize to target resolution
            segment_clip = segment_clip.resize(resolution)

            # Apply effects if any
            if segment.effects:
                segment_clip = self._apply_effects_to_clip(segment_clip, segment.effects)

            clip.close()
            return segment_clip

        except Exception as e:
            logger.error(f"Error processing segment {segment.video_path}: {e}")
            return None

    def _apply_effects_to_clip(self, clip: VideoFileClip, effects: List[str]) -> VideoFileClip:
        """
        Apply effects to a video clip.

        Args:
            clip: Input video clip
            effects: List of effect names

        Returns:
            Clip with effects applied
        """
        # For complex effects that require frame-by-frame processing,
        # we need to extract frames, apply effects, and recreate the clip

        if any(effect in effects for effect in ['datamosh', 'color_bleed', 'glitch']):
            return self._apply_frame_effects(clip, effects)
        else:
            return self._apply_simple_effects(clip, effects)

    def _apply_simple_effects(self, clip: VideoFileClip, effects: List[str]) -> VideoFileClip:
        """Apply simple effects using MoviePy."""

        for effect in effects:
            if effect == "fade_in":
                clip = clip.fadein(1.0)
            elif effect == "fade_out":
                clip = clip.fadeout(1.0)
            elif effect == "slow_motion":
                clip = clip.fx(lambda c: c.speedx(0.5))
            elif effect == "fast_motion":
                clip = clip.fx(lambda c: c.speedx(2.0))
            elif effect == "black_and_white":
                clip = clip.fx(lambda c: c.fx(lambda frame: cv2.cvtColor(cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY), cv2.COLOR_GRAY2RGB)))

        return clip

    def _apply_frame_effects(self, clip: VideoFileClip, effects: List[str]) -> VideoFileClip:
        """Apply frame-by-frame effects."""

        # Extract frames
        frames = []
        for t in np.arange(0, clip.duration, 1.0/clip.fps):
            frame = clip.get_frame(t)
            # Convert RGB to BGR for OpenCV
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            frames.append(frame_bgr)

        # Apply effects
        processed_frames = frames.copy()

        for effect in effects:
            if effect == "datamosh":
                processed_frames = self.datamosh_effect.apply_datamosh(processed_frames)
            elif effect == "color_bleed":
                processed_frames = [self.color_effects.apply_color_bleed(frame) for frame in processed_frames]
            elif effect == "color_shift":
                processed_frames = [self.color_effects.apply_color_shift(frame) for frame in processed_frames]
            elif effect == "false_color":
                processed_frames = [self.color_effects.apply_false_color(frame) for frame in processed_frames]
            elif effect == "posterize":
                processed_frames = [self.color_effects.apply_posterization(frame) for frame in processed_frames]
            elif effect == "chromatic_aberration":
                processed_frames = [self.color_effects.apply_chromatic_aberration(frame) for frame in processed_frames]
            elif effect == "wave_distortion":
                processed_frames = [self.frame_warping.apply_wave_distortion(frame) for frame in processed_frames]
            elif effect == "spiral_distortion":
                processed_frames = [self.frame_warping.apply_spiral_distortion(frame) for frame in processed_frames]
            elif effect == "fisheye":
                processed_frames = [self.frame_warping.apply_fisheye_distortion(frame) for frame in processed_frames]
            elif effect == "kaleidoscope":
                processed_frames = [self.frame_warping.apply_kaleidoscope(frame) for frame in processed_frames]
            elif effect == "mirror":
                processed_frames = [self.frame_warping.apply_mirror_effect(frame) for frame in processed_frames]
            elif effect == "pixelation":
                processed_frames = [self.frame_warping.apply_pixelation(frame) for frame in processed_frames]
            elif effect == "recursive_zoom":
                processed_frames = [self.frame_warping.apply_recursive_zoom(frame) for frame in processed_frames]
            elif effect == "video_feedback":
                processed_frames = self.recursive_effects.apply_video_feedback(processed_frames)
            elif effect == "temporal_echo":
                processed_frames = self.recursive_effects.apply_temporal_echo(processed_frames)
            elif effect == "infinite_zoom":
                processed_frames = self.recursive_effects.apply_infinite_zoom(processed_frames)
            elif effect == "fractal_feedback":
                processed_frames = [self.recursive_effects.apply_fractal_feedback(frame) for frame in processed_frames]
            elif effect == "digital_noise":
                processed_frames = [self.glitch_effects.apply_digital_noise(frame) for frame in processed_frames]
            elif effect == "scan_lines":
                processed_frames = [self.glitch_effects.apply_scan_lines(frame) for frame in processed_frames]
            elif effect == "pixel_sorting":
                processed_frames = [self.glitch_effects.apply_pixel_sorting(frame) for frame in processed_frames]
            elif effect == "rgb_shift":
                processed_frames = [self.glitch_effects.apply_rgb_shift(frame) for frame in processed_frames]
            elif effect == "block_corruption":
                processed_frames = [self.glitch_effects.apply_block_corruption(frame) for frame in processed_frames]
            elif effect == "vhs_artifacts":
                processed_frames = [self.glitch_effects.apply_vhs_artifacts(frame) for frame in processed_frames]
            elif effect == "bit_crush":
                processed_frames = [self.glitch_effects.apply_compression_artifacts(frame) for frame in processed_frames]

        # Convert back to RGB and create new clip
        rgb_frames = []
        for frame in processed_frames:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            rgb_frames.append(rgb_frame)

        # Create new clip from processed frames
        def make_frame(t):
            frame_index = int(t * clip.fps)
            if frame_index >= len(rgb_frames):
                frame_index = len(rgb_frames) - 1
            return rgb_frames[frame_index]

        try:
            from moviepy.video.VideoClip import VideoClip
        except ImportError:
            try:
                from moviepy import VideoClip
            except ImportError:
                # Create a simple VideoClip class if import fails
                class VideoClip:
                    def __init__(self, make_frame, duration):
                        self.make_frame = make_frame
                        self.duration = duration
                        self.fps = 24
        processed_clip = VideoClip(make_frame, duration=clip.duration)
        processed_clip.fps = clip.fps

        # Copy audio if present
        if clip.audio:
            processed_clip = processed_clip.set_audio(clip.audio)

        return processed_clip

    def clear_segments(self):
        """Clear all segments from the composition."""
        self.segments.clear()

    def get_total_duration(self) -> float:
        """Get total duration of all segments."""
        return sum(segment.target_duration for segment in self.segments)

    def preview_segment(self, segment_index: int, output_path: Path) -> bool:
        """
        Render a preview of a specific segment.

        Args:
            segment_index: Index of segment to preview
            output_path: Output path for preview

        Returns:
            True if preview was created successfully
        """
        if segment_index >= len(self.segments):
            logger.error(f"Segment index {segment_index} out of range")
            return False

        segment = self.segments[segment_index]

        try:
            clip = self._process_segment(segment, 30, (1280, 720))

            if clip:
                clip.write_videofile(str(output_path), fps=30)
                clip.close()
                return True

        except Exception as e:
            logger.error(f"Error creating preview: {e}")

        return False
