"""
YouTube video downloader using yt-dlp.
"""

import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
import yt_dlp
from config import config

logger = logging.getLogger(__name__)


class YouTubeDownloader:
    """Downloads videos from YouTube using yt-dlp."""
    
    def __init__(self, output_dir: Optional[Path] = None):
        self.output_dir = output_dir or config.video.input_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # yt-dlp options
        self.ydl_opts = {
            'format': f'best[height<={config.video.youtube_quality[:-1]}]',
            'outtmpl': str(self.output_dir / '%(title)s.%(ext)s'),
            'writeinfojson': True,  # Save metadata
            'writesubtitles': True,  # Download subtitles if available
            'writeautomaticsub': True,  # Download auto-generated subtitles
            'subtitleslangs': ['en'],
            'ignoreerrors': True,
            'no_warnings': False,
        }
    
    def download_video(self, url: str, output_dir: Optional[Path] = None) -> Optional[Path]:
        """
        Download a single video from YouTube.
        
        Args:
            url: YouTube video URL
            output_dir: Optional output directory override
            
        Returns:
            Path to downloaded video file, or None if failed
        """
        if output_dir:
            opts = self.ydl_opts.copy()
            opts['outtmpl'] = str(output_dir / '%(title)s.%(ext)s')
        else:
            opts = self.ydl_opts
        
        try:
            with yt_dlp.YoutubeDL(opts) as ydl:
                # Get video info first
                info = ydl.extract_info(url, download=False)
                
                # Check duration
                duration = info.get('duration', 0)
                if duration > config.video.max_video_duration:
                    logger.warning(f"Video too long ({duration}s), skipping: {info.get('title', url)}")
                    return None
                
                # Download the video
                ydl.download([url])
                
                # Find the downloaded file
                title = info.get('title', 'unknown')
                ext = info.get('ext', 'mp4')
                video_path = (output_dir or self.output_dir) / f"{title}.{ext}"
                
                if video_path.exists():
                    logger.info(f"Downloaded: {video_path}")
                    return video_path
                else:
                    # Try to find the file with a sanitized name
                    for file in (output_dir or self.output_dir).glob(f"*{title[:20]}*.{ext}"):
                        logger.info(f"Downloaded: {file}")
                        return file
                    
                    logger.error(f"Could not find downloaded file for: {title}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error downloading {url}: {e}")
            return None
    
    def download_playlist(self, playlist_url: str, output_dir: Optional[Path] = None, max_videos: int = 50) -> List[Path]:
        """
        Download videos from a YouTube playlist.
        
        Args:
            playlist_url: YouTube playlist URL
            output_dir: Optional output directory override
            max_videos: Maximum number of videos to download
            
        Returns:
            List of paths to downloaded video files
        """
        if output_dir:
            opts = self.ydl_opts.copy()
            opts['outtmpl'] = str(output_dir / '%(title)s.%(ext)s')
        else:
            opts = self.ydl_opts
        
        # Add playlist-specific options
        opts.update({
            'playlistend': max_videos,
            'extract_flat': False,
        })
        
        downloaded_files = []
        
        try:
            with yt_dlp.YoutubeDL(opts) as ydl:
                # Get playlist info
                playlist_info = ydl.extract_info(playlist_url, download=False)
                entries = playlist_info.get('entries', [])
                
                logger.info(f"Found {len(entries)} videos in playlist")
                
                for entry in entries[:max_videos]:
                    if entry is None:
                        continue
                    
                    video_url = entry.get('webpage_url') or entry.get('url')
                    if video_url:
                        video_path = self.download_video(video_url, output_dir)
                        if video_path:
                            downloaded_files.append(video_path)
                
        except Exception as e:
            logger.error(f"Error downloading playlist {playlist_url}: {e}")
        
        logger.info(f"Downloaded {len(downloaded_files)} videos from playlist")
        return downloaded_files
    
    def get_video_info(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Get video information without downloading.
        
        Args:
            url: YouTube video URL
            
        Returns:
            Video information dictionary
        """
        try:
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                return {
                    'title': info.get('title'),
                    'description': info.get('description'),
                    'duration': info.get('duration'),
                    'upload_date': info.get('upload_date'),
                    'uploader': info.get('uploader'),
                    'view_count': info.get('view_count'),
                    'tags': info.get('tags', []),
                    'categories': info.get('categories', []),
                }
        except Exception as e:
            logger.error(f"Error getting video info for {url}: {e}")
            return None
