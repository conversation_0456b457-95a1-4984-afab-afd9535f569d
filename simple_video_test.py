"""
Simple video test using OpenCV only.
"""

import cv2
import numpy as np
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_experimental_video_opencv():
    """Create experimental video using only OpenCV."""
    
    logger.info("Creating experimental video with OpenCV...")
    
    try:
        # Import effects
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from effects.datamosh import DatamoshEffect
        from video_analysis.frame_extractor import FrameExtractor
        
        # Initialize effects
        color_fx = ColorEffects()
        warp_fx = FrameWarpingEffect()
        glitch_fx = GlitchEffects()
        datamosh_fx = DatamoshEffect()
        extractor = FrameExtractor()
        
        # Get source videos
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found")
            return False
        
        # Output settings
        output_path = Path("./output/experimental_opencv.mp4")
        output_path.parent.mkdir(exist_ok=True)
        
        fps = 24
        resolution = (1280, 720)
        
        # Create video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, resolution)
        
        if not out.isOpened():
            logger.error("Failed to create video writer")
            return False
        
        total_frames_written = 0
        target_duration = 20  # 20 seconds
        target_frames = fps * target_duration
        
        logger.info(f"Target: {target_frames} frames ({target_duration}s at {fps}fps)")
        
        # Process each video
        for i, video_file in enumerate(video_files[:3]):  # Use first 3 videos
            logger.info(f"Processing video {i+1}: {video_file.name}")
            
            # Extract frames from this video
            frames = extractor.extract_frames(video_file, start_time=0, end_time=8)
            
            if not frames:
                logger.warning(f"No frames extracted from {video_file}")
                continue
            
            # Convert frames to list
            frame_list = [frame for _, frame in frames]
            
            # Apply different effects to each video
            if i == 0:
                # Color effects
                logger.info("Applying color effects...")
                processed_frames = []
                for frame in frame_list:
                    # Apply multiple color effects
                    frame = color_fx.apply_color_bleed(frame, intensity=0.4)
                    frame = color_fx.apply_color_shift(frame)
                    processed_frames.append(frame)
                
            elif i == 1:
                # Warping effects
                logger.info("Applying warping effects...")
                processed_frames = []
                for frame in frame_list:
                    frame = warp_fx.apply_wave_distortion(frame, amplitude=15)
                    frame = warp_fx.apply_spiral_distortion(frame, intensity=0.3)
                    processed_frames.append(frame)
                
            else:
                # Glitch effects
                logger.info("Applying glitch effects...")
                processed_frames = []
                for frame in frame_list:
                    frame = glitch_fx.apply_digital_noise(frame, intensity=0.2)
                    frame = glitch_fx.apply_rgb_shift(frame, shift_amount=3)
                    frame = glitch_fx.apply_scan_lines(frame)
                    processed_frames.append(frame)
            
            # Apply datamosh to the sequence
            if len(processed_frames) > 3:
                logger.info("Applying datamosh...")
                processed_frames = datamosh_fx.apply_datamosh(processed_frames, intensity=0.3)
            
            # Write frames to video
            for frame in processed_frames:
                if total_frames_written >= target_frames:
                    break
                
                # Resize frame to target resolution
                frame_resized = cv2.resize(frame, resolution)
                
                # Convert BGR to RGB if needed (OpenCV uses BGR)
                out.write(frame_resized)
                total_frames_written += 1
                
                if total_frames_written % 50 == 0:
                    logger.info(f"Written {total_frames_written}/{target_frames} frames")
            
            if total_frames_written >= target_frames:
                break
        
        # Fill remaining frames if needed
        if total_frames_written < target_frames:
            logger.info(f"Filling remaining {target_frames - total_frames_written} frames...")
            # Create some abstract frames
            for _ in range(target_frames - total_frames_written):
                # Create a noise frame
                noise_frame = np.random.randint(0, 256, (resolution[1], resolution[0], 3), dtype=np.uint8)
                noise_frame = glitch_fx.apply_digital_noise(noise_frame, intensity=0.5)
                out.write(noise_frame)
                total_frames_written += 1
        
        # Release video writer
        out.release()
        
        # Check if file was created
        if output_path.exists():
            file_size = output_path.stat().st_size / 1024 / 1024
            logger.info(f"✅ Experimental video created: {output_path}")
            logger.info(f"Duration: {total_frames_written / fps:.1f}s")
            logger.info(f"File size: {file_size:.1f} MB")
            logger.info(f"Resolution: {resolution[0]}x{resolution[1]}")
            logger.info(f"FPS: {fps}")
            return True
        else:
            logger.error("❌ Video file was not created")
            return False
        
    except Exception as e:
        logger.error(f"Error creating experimental video: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_effects_showcase():
    """Create a showcase of all effects."""
    
    logger.info("Creating effects showcase...")
    
    try:
        from effects.color_effects import ColorEffects
        from effects.frame_warping import FrameWarpingEffect
        from effects.glitch_effects import GlitchEffects
        from video_analysis.frame_extractor import FrameExtractor
        
        # Get a source video
        video_dir = Path("C:/Quick Share")
        video_files = list(video_dir.glob("*.mp4"))
        
        if not video_files:
            logger.error("No video files found")
            return False
        
        source_video = video_files[0]
        logger.info(f"Using source video: {source_video.name}")
        
        # Extract frames
        extractor = FrameExtractor()
        frames = extractor.extract_frames(source_video, start_time=0, end_time=3)
        
        if len(frames) < 5:
            logger.error("Not enough frames extracted")
            return False
        
        # Get a test frame
        _, test_frame = frames[0]
        
        # Initialize effects
        color_fx = ColorEffects()
        warp_fx = FrameWarpingEffect()
        glitch_fx = GlitchEffects()
        
        # Create output directory
        output_dir = Path("./output/effects_showcase")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save original
        cv2.imwrite(str(output_dir / "00_original.jpg"), test_frame)
        
        # Test all effects
        effects_list = [
            ("01_color_bleed", lambda f: color_fx.apply_color_bleed(f)),
            ("02_color_shift", lambda f: color_fx.apply_color_shift(f)),
            ("03_false_color", lambda f: color_fx.apply_false_color(f)),
            ("04_chromatic_aberration", lambda f: color_fx.apply_chromatic_aberration(f)),
            ("05_wave_distortion", lambda f: warp_fx.apply_wave_distortion(f)),
            ("06_spiral_distortion", lambda f: warp_fx.apply_spiral_distortion(f)),
            ("07_fisheye", lambda f: warp_fx.apply_fisheye_distortion(f)),
            ("08_kaleidoscope", lambda f: warp_fx.apply_kaleidoscope(f)),
            ("09_mirror", lambda f: warp_fx.apply_mirror_effect(f)),
            ("10_pixelation", lambda f: warp_fx.apply_pixelation(f, pixel_size=8)),
            ("11_digital_noise", lambda f: glitch_fx.apply_digital_noise(f)),
            ("12_scan_lines", lambda f: glitch_fx.apply_scan_lines(f)),
            ("13_rgb_shift", lambda f: glitch_fx.apply_rgb_shift(f)),
            ("14_vhs_artifacts", lambda f: glitch_fx.apply_vhs_artifacts(f)),
        ]
        
        logger.info(f"Applying {len(effects_list)} effects...")
        
        for name, effect_func in effects_list:
            try:
                logger.info(f"Applying {name}...")
                result_frame = effect_func(test_frame.copy())
                cv2.imwrite(str(output_dir / f"{name}.jpg"), result_frame)
            except Exception as e:
                logger.error(f"Error applying {name}: {e}")
        
        logger.info(f"✅ Effects showcase created in {output_dir}")
        return True
        
    except Exception as e:
        logger.error(f"Error creating effects showcase: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    logger.info("🎬 Starting Simple Video Test")
    
    # Create effects showcase
    if create_effects_showcase():
        logger.info("✅ Effects showcase completed")
    else:
        logger.error("❌ Effects showcase failed")
    
    # Create experimental video
    if create_experimental_video_opencv():
        logger.info("✅ Experimental video completed")
    else:
        logger.error("❌ Experimental video failed")
    
    logger.info("🎉 Simple video test completed!")
